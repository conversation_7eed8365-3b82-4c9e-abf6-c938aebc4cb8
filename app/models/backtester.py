#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Backtesting module for the Trend Prediction Service.
This module provides functionality for evaluating model performance on historical data.
"""

import os
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Union, Any
from datetime import datetime
import json
import matplotlib.pyplot as plt
from io import BytesIO
import base64

from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_curve, auc
)

from app.models.predictor import TrendPredictor
from app.utils.data_processor import DataProcessor
from app.utils.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class ModelBacktester:
    """
    Class for backtesting model performance on historical data.
    """

    def __init__(self):
        """Initialize the backtester."""
        self.data_processor = DataProcessor()

    def backtest(self, model_path: str, data_source: Dict[str, Any],
                window: int = 24, generate_plots: bool = True,
                test_split: float = 0.2) -> Dict[str, Any]:
        """
        Backtest a model on historical data with proper temporal validation.

        Args:
            model_path (str): Path to the model file.
            data_source (Dict[str, Any]): Source of data for backtesting.
                Either {'market_data': List[Dict]} or {'data_path': str}.
            window (int): Prediction window in hours.
            generate_plots (bool): Whether to generate performance plots.
            test_split (float): Fraction of data to use for testing (most recent data).

        Returns:
            Dict[str, Any]: Backtesting results including metrics and plots.

        Raises:
            ValueError: If the model or data is invalid.
        """
        try:
            # Load data
            if 'market_data' in data_source:
                # Convert market data to DataFrame
                df = pd.DataFrame(data_source['market_data'])

                # Convert timestamps to datetime
                df['date_timestamp'] = [datetime.utcfromtimestamp(int(ts/1000))
                                       for ts in df['timestamp']]
            elif 'data_path' in data_source:
                # Load data from CSV file
                df = pd.read_csv(data_source['data_path'])

                # Convert timestamps to datetime if needed
                if 'timestamp' in df.columns and 'date_timestamp' not in df.columns:
                    df['date_timestamp'] = [datetime.utcfromtimestamp(int(ts/1000))
                                           for ts in df['timestamp']]
            else:
                raise ValueError("Invalid data source. Must provide either 'market_data' or 'data_path'.")

            # Create target variable (future price movement)
            target_class = (df.shift(-window)['close'] >= df['close'])
            df['target_class'] = target_class.astype(int)

            # Drop rows with NaN in target_class
            df = df.dropna().reset_index(drop=True)

            # Use temporal split for proper backtesting (most recent data for testing)
            split_idx = int(len(df) * (1 - test_split))
            test_df = df.iloc[split_idx:].copy()

            logger.info(f"Backtesting on {len(test_df)} samples (last {test_split*100:.1f}% of data)")
            logger.info(f"Test period: index {split_idx} to {len(df)-1}")

            # Load the trained model directly
            import tensorflow as tf
            model = tf.keras.models.load_model(model_path)
            logger.info(f"Loaded model from {model_path}")

            # Process the test data using the same data processor
            from app.utils.data_processor import DataProcessor
            data_processor = DataProcessor()

            # Get the full dataset for proper processing
            full_df = pd.read_csv(data_source['data_path'])

            # CRITICAL: Use a completely separate test split for backtesting
            # This ensures no data leakage between training and testing

            # Calculate technical indicators first
            df_with_indicators = data_processor._calculate_indicators(full_df)
            df_with_indicators = data_processor._get_moving_average_labels(df_with_indicators)

            # Drop unnecessary columns and NaN values
            df_with_indicators = df_with_indicators.drop(
                ['timestamp', 'date_timestamp', 'end_timestamp', 'num_trade'],
                axis=1, errors='ignore'
            )
            df_with_indicators = df_with_indicators.dropna()

            # CRITICAL: Use data AFTER the training/validation split to avoid data leakage
            # Training uses first 80%, validation uses next 20%
            # We use the VERY LAST 5% for backtesting (completely unseen)
            training_split = 0.8  # 80% for training
            validation_split = 0.2  # 20% for validation
            test_split = 0.05  # 5% for backtesting (completely separate)

            # Calculate split indices
            train_end_idx = int(len(df_with_indicators) * training_split)
            val_end_idx = int(len(df_with_indicators) * (training_split + validation_split * 0.75))  # Use only 75% of validation data
            test_start_idx = val_end_idx  # Start test right after validation

            test_df = df_with_indicators.iloc[test_start_idx:].copy()

            logger.info(f"Data splits:")
            logger.info(f"  Training: 0 to {train_end_idx} ({train_end_idx} samples)")
            logger.info(f"  Validation: {train_end_idx} to {val_end_idx} ({val_end_idx - train_end_idx} samples)")
            logger.info(f"  Backtesting: {test_start_idx} to {len(df_with_indicators)} ({len(test_df)} samples)")
            logger.info(f"Backtesting on {len(test_df)} samples (completely unseen data)")
            logger.info(f"Test period: index {test_start_idx} to {len(df_with_indicators)-1}")

            # Prepare test features and labels
            feature_columns = [col for col in test_df.columns
                             if col not in ['open', 'high', 'low', 'close', 'volume', 'target_class']]

            X_test = test_df[feature_columns].values
            y_test_raw = test_df['target_class'].values

            # Scale features using the same scaler (fit on training data)
            X_test_scaled = data_processor.scaler.fit_transform(X_test)

            # Transform to image format
            x_test = data_processor._transform_to_image(X_test_scaled)

            # One-hot encode labels
            y_test_reshaped = y_test_raw.reshape(-1, 1)
            y_test = data_processor.one_hot_encoder.fit_transform(y_test_reshaped)

            logger.info(f"Backtesting on {len(x_test)} samples with shape {x_test.shape}")

            # Make predictions using the model directly
            y_pred_proba = model.predict(x_test, verbose=0)
            y_pred_classes = np.argmax(y_pred_proba, axis=1)
            y_true_classes = np.argmax(y_test, axis=1)

            # Get prediction probabilities (confidence)
            probabilities = np.max(y_pred_proba, axis=1)

            # Create a simplified test dataframe for metrics calculation
            test_df = pd.DataFrame({
                'target_class': y_true_classes,
                'prediction': y_pred_classes,
                'probability': probabilities
            })

            # Calculate metrics on test data only
            y_true = test_df['target_class'].values
            y_pred = test_df['prediction'].values

            metrics = {
                'accuracy': float(accuracy_score(y_true, y_pred)),
                'precision': float(precision_score(y_true, y_pred, zero_division=0)),
                'recall': float(recall_score(y_true, y_pred, zero_division=0)),
                'f1_score': float(f1_score(y_true, y_pred, zero_division=0)),
            }

            # Calculate ROC curve and AUC
            fpr, tpr, _ = roc_curve(y_true, test_df['probability'].values)
            metrics['auc'] = float(auc(fpr, tpr))

            # Generate confusion matrix
            cm = confusion_matrix(y_true, y_pred)
            metrics['confusion_matrix'] = cm.tolist()

            # Generate classification report
            report = classification_report(y_true, y_pred, output_dict=True)
            metrics['classification_report'] = report

            # Calculate trading metrics
            trading_metrics = self._calculate_trading_metrics(test_df)
            metrics.update(trading_metrics)

            # Generate plots if requested
            plots = {}
            if generate_plots:
                # For simplified backtesting, we'll generate basic plots
                plots = self._generate_simple_plots(metrics, fpr, tpr)

            # Save results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_dir = os.path.join(settings.MODEL_DIR, 'backtest_results')
            os.makedirs(results_dir, exist_ok=True)

            results_path = os.path.join(results_dir, f"backtest_{timestamp}.json")

            # Save metrics and plots
            results = {
                'timestamp': timestamp,
                'model_path': model_path,
                'window': window,
                'metrics': metrics,
                'plots': plots
            }

            with open(results_path, 'w') as f:
                json.dump(results, f, indent=2)

            logger.info(f"Backtesting completed. Results saved to {results_path}")

            return results

        except Exception as e:
            logger.error(f"Backtesting error: {str(e)}")
            raise ValueError(f"Failed to backtest model: {str(e)}")

    def _calculate_trading_metrics(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate trading performance metrics.

        Args:
            df (pd.DataFrame): DataFrame with predictions and actual values.

        Returns:
            Dict[str, float]: Trading metrics.
        """
        # Initialize metrics
        metrics = {}

        # Calculate win rate
        correct_predictions = (df['prediction'] == df['target_class']).sum()
        total_predictions = len(df)
        win_rate = correct_predictions / total_predictions if total_predictions > 0 else 0
        metrics['win_rate'] = float(win_rate)

        # Calculate profit factor
        # Assuming 1% gain on correct up predictions and 1% loss on incorrect up predictions
        up_predictions = df[df['prediction'] == 1]
        correct_ups = up_predictions[up_predictions['target_class'] == 1]
        incorrect_ups = up_predictions[up_predictions['target_class'] == 0]

        gross_profit = len(correct_ups) * 0.01
        gross_loss = len(incorrect_ups) * 0.01

        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        metrics['profit_factor'] = float(profit_factor) if profit_factor != float('inf') else 999.99

        # Calculate maximum drawdown
        cumulative_returns = np.zeros(len(df))

        for i in range(len(df)):
            if df.iloc[i]['prediction'] == 1:  # Up prediction
                if df.iloc[i]['target_class'] == 1:  # Correct
                    cumulative_returns[i] = 0.01
                else:  # Incorrect
                    cumulative_returns[i] = -0.01

        cumulative_returns = np.cumsum(cumulative_returns)
        max_return = np.maximum.accumulate(cumulative_returns)
        drawdown = max_return - cumulative_returns
        max_drawdown = np.max(drawdown)

        metrics['max_drawdown'] = float(max_drawdown)

        # Calculate Sharpe ratio (assuming risk-free rate of 0)
        returns = np.diff(cumulative_returns, prepend=0)
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(365) if np.std(returns) > 0 else 0
        metrics['sharpe_ratio'] = float(sharpe_ratio)

        # Calculate total return
        total_return = cumulative_returns[-1]
        metrics['total_return'] = float(total_return)

        return metrics

    def _generate_performance_plots(self, df: pd.DataFrame, metrics: Dict[str, Any],
                                   fpr: np.ndarray, tpr: np.ndarray) -> Dict[str, str]:
        """
        Generate performance plots.

        Args:
            df (pd.DataFrame): DataFrame with predictions and actual values.
            metrics (Dict[str, Any]): Performance metrics.
            fpr (np.ndarray): False positive rates for ROC curve.
            tpr (np.ndarray): True positive rates for ROC curve.

        Returns:
            Dict[str, str]: Base64-encoded plot images.
        """
        plots = {}

        # Create figure for price and predictions
        plt.figure(figsize=(12, 6))
        plt.plot(df['date_timestamp'], df['close'], label='Close Price')

        # Plot buy signals (up predictions)
        buy_signals = df[df['prediction'] == 1]
        plt.scatter(buy_signals['date_timestamp'], buy_signals['close'],
                   color='green', label='Up Prediction', alpha=0.5, marker='^')

        # Plot sell signals (down predictions)
        sell_signals = df[df['prediction'] == 0]
        plt.scatter(sell_signals['date_timestamp'], sell_signals['close'],
                   color='red', label='Down Prediction', alpha=0.5, marker='v')

        plt.title('Price Chart with Predictions')
        plt.xlabel('Date')
        plt.ylabel('Price')
        plt.legend()
        plt.grid(True)

        # Save plot to BytesIO object
        buf = BytesIO()
        plt.savefig(buf, format='png')
        buf.seek(0)

        # Convert to base64
        price_plot = base64.b64encode(buf.read()).decode('utf-8')
        plots['price_plot'] = price_plot
        plt.close()

        # Create ROC curve
        plt.figure(figsize=(8, 6))
        plt.plot(fpr, tpr, color='darkorange', lw=2,
                label=f'ROC curve (area = {metrics["auc"]:.2f})')
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('Receiver Operating Characteristic')
        plt.legend(loc="lower right")

        # Save plot to BytesIO object
        buf = BytesIO()
        plt.savefig(buf, format='png')
        buf.seek(0)

        # Convert to base64
        roc_plot = base64.b64encode(buf.read()).decode('utf-8')
        plots['roc_plot'] = roc_plot
        plt.close()

        # Create confusion matrix plot
        cm = np.array(metrics['confusion_matrix'])
        plt.figure(figsize=(8, 6))
        plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
        plt.title('Confusion Matrix')
        plt.colorbar()

        classes = ['Down', 'Up']
        tick_marks = np.arange(len(classes))
        plt.xticks(tick_marks, classes)
        plt.yticks(tick_marks, classes)

        # Add text annotations
        thresh = cm.max() / 2.
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                plt.text(j, i, format(cm[i, j], 'd'),
                        ha="center", va="center",
                        color="white" if cm[i, j] > thresh else "black")

        plt.ylabel('True label')
        plt.xlabel('Predicted label')

        # Save plot to BytesIO object
        buf = BytesIO()
        plt.savefig(buf, format='png')
        buf.seek(0)

        # Convert to base64
        cm_plot = base64.b64encode(buf.read()).decode('utf-8')
        plots['confusion_matrix_plot'] = cm_plot
        plt.close()

        # Create cumulative returns plot
        cumulative_returns = np.zeros(len(df))

        for i in range(len(df)):
            if df.iloc[i]['prediction'] == 1:  # Up prediction
                if df.iloc[i]['target_class'] == 1:  # Correct
                    cumulative_returns[i] = 0.01
                else:  # Incorrect
                    cumulative_returns[i] = -0.01

        cumulative_returns = np.cumsum(cumulative_returns)

        plt.figure(figsize=(12, 6))
        plt.plot(df['date_timestamp'], cumulative_returns, label='Cumulative Return')
        plt.title('Cumulative Returns')
        plt.xlabel('Date')
        plt.ylabel('Return')
        plt.grid(True)
        plt.legend()

        # Save plot to BytesIO object
        buf = BytesIO()
        plt.savefig(buf, format='png')
        buf.seek(0)

        # Convert to base64
        returns_plot = base64.b64encode(buf.read()).decode('utf-8')
        plots['returns_plot'] = returns_plot
        plt.close()

        return plots

    def _generate_simple_plots(self, metrics: Dict[str, Any], fpr: np.ndarray, tpr: np.ndarray) -> Dict[str, str]:
        """
        Generate simplified performance plots for backtesting.

        Args:
            metrics (Dict[str, Any]): Performance metrics.
            fpr (np.ndarray): False positive rates for ROC curve.
            tpr (np.ndarray): True positive rates for ROC curve.

        Returns:
            Dict[str, str]: Base64-encoded plot images.
        """
        plots = {}

        # Create ROC curve
        plt.figure(figsize=(8, 6))
        plt.plot(fpr, tpr, color='darkorange', lw=2,
                label=f'ROC curve (area = {metrics["auc"]:.2f})')
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('Receiver Operating Characteristic')
        plt.legend(loc="lower right")

        # Save plot to BytesIO object
        buf = BytesIO()
        plt.savefig(buf, format='png')
        buf.seek(0)

        # Convert to base64
        roc_plot = base64.b64encode(buf.read()).decode('utf-8')
        plots['roc_plot'] = roc_plot
        plt.close()

        # Create confusion matrix plot
        cm = np.array(metrics['confusion_matrix'])
        plt.figure(figsize=(8, 6))
        plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
        plt.title('Confusion Matrix')
        plt.colorbar()

        classes = ['Down', 'Up']
        tick_marks = np.arange(len(classes))
        plt.xticks(tick_marks, classes)
        plt.yticks(tick_marks, classes)

        # Add text annotations
        thresh = cm.max() / 2.
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                plt.text(j, i, format(cm[i, j], 'd'),
                        ha="center", va="center",
                        color="white" if cm[i, j] > thresh else "black")

        plt.ylabel('True label')
        plt.xlabel('Predicted label')

        # Save plot to BytesIO object
        buf = BytesIO()
        plt.savefig(buf, format='png')
        buf.seek(0)

        # Convert to base64
        cm_plot = base64.b64encode(buf.read()).decode('utf-8')
        plots['confusion_matrix_plot'] = cm_plot
        plt.close()

        return plots

    def get_backtest_results(self, results_id: str = None) -> List[Dict[str, Any]]:
        """
        Get backtesting results.

        Args:
            results_id (str, optional): ID of specific results to retrieve.
                If None, returns all results.

        Returns:
            List[Dict[str, Any]]: List of backtesting results.
        """
        results_dir = os.path.join(settings.MODEL_DIR, 'backtest_results')

        if not os.path.exists(results_dir):
            return []

        results_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]

        if results_id:
            results_files = [f for f in results_files if results_id in f]

        results = []
        for file in results_files:
            try:
                with open(os.path.join(results_dir, file), 'r') as f:
                    result = json.load(f)
                    results.append(result)
            except Exception as e:
                logger.warning(f"Error loading results from {file}: {str(e)}")

        # Sort by timestamp (newest first)
        results.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

        return results

    def compare_models(self, model_paths: List[str], data_source: Dict[str, Any],
                      window: int = 24) -> Dict[str, Any]:
        """
        Compare multiple models on the same dataset.

        Args:
            model_paths (List[str]): List of model paths to compare.
            data_source (Dict[str, Any]): Source of data for backtesting.
            window (int): Prediction window in hours.

        Returns:
            Dict[str, Any]: Comparison results.
        """
        results = {}

        for model_path in model_paths:
            try:
                model_name = os.path.basename(model_path)
                logger.info(f"Backtesting model: {model_name}")

                # Run backtest
                backtest_result = self.backtest(
                    model_path=model_path,
                    data_source=data_source,
                    window=window,
                    generate_plots=False  # Skip plots for comparison
                )

                # Extract metrics
                results[model_name] = backtest_result['metrics']

            except Exception as e:
                logger.error(f"Error backtesting model {model_path}: {str(e)}")
                results[os.path.basename(model_path)] = {"error": str(e)}

        # Find best model based on different metrics
        best_models = {}

        if results:
            metrics_to_compare = ['accuracy', 'f1_score', 'auc', 'profit_factor', 'sharpe_ratio']

            for metric in metrics_to_compare:
                try:
                    # Filter models that have this metric
                    valid_models = {k: v for k, v in results.items() if metric in v and not isinstance(v, str)}

                    if valid_models:
                        # Find model with highest metric value
                        best_model = max(valid_models.items(), key=lambda x: x[1][metric])
                        best_models[f"best_{metric}"] = {
                            "model": best_model[0],
                            "value": best_model[1][metric]
                        }
                except Exception as e:
                    logger.warning(f"Error finding best model for {metric}: {str(e)}")

        # Save comparison results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = os.path.join(settings.MODEL_DIR, 'backtest_results')
        os.makedirs(results_dir, exist_ok=True)

        comparison_path = os.path.join(results_dir, f"comparison_{timestamp}.json")

        comparison_results = {
            'timestamp': timestamp,
            'models': model_paths,
            'results': results,
            'best_models': best_models
        }

        with open(comparison_path, 'w') as f:
            json.dump(comparison_results, f, indent=2)

        logger.info(f"Model comparison completed. Results saved to {comparison_path}")

        return comparison_results
