#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Predictor module for the Trend Prediction Service.
This module provides functionality for making predictions using trained models.
"""

import os
import logging
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Union, Any
from datetime import datetime

import tensorflow as tf
from tensorflow.keras.models import load_model

from app.utils.data_processor import DataProcessor
from app.utils.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class TrendPredictor:
    """
    Class for making predictions using trained CNN models.
    """

    def __init__(self, model_path: str = None, pair: str = None):
        """
        Initialize the predictor with a trained model.

        Args:
            model_path (str, optional): Path to the trained model file (.h5).
                If None, the default model for the specified pair will be used.
            pair (str, optional): Trading pair symbol (e.g., "BTCUSDT").
                If None, the default pair will be used.
        """
        self.model = None
        self.data_processor = DataProcessor()
        self.pair = pair or settings.DEFAULT_PAIR
        self.model_path = None

        # Check if pair is supported
        if not settings.is_pair_supported(self.pair):
            logger.warning(f"Pair {self.pair} is not supported")
            return

        # Load model
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            # Try to load the default model for this pair
            default_model_path = settings.get_default_model_path(self.pair)
            if os.path.exists(default_model_path):
                self.load_model(default_model_path)
            else:
                logger.warning(f"No model found for pair {self.pair}")

    def load_model(self, model_path: str) -> None:
        """
        Load a trained model from the specified path.

        Args:
            model_path (str): Path to the trained model file (.h5).

        Raises:
            FileNotFoundError: If the model file does not exist.
            ValueError: If the model cannot be loaded.
        """
        try:
            logger.info(f"Loading model from {model_path}")
            self.model = load_model(model_path, compile=False)
            self.model_path = model_path

            # Compile the model
            self.model.compile(
                loss='binary_crossentropy',
                optimizer='adam',
                metrics=['accuracy']
            )

            # Update usage statistics
            self._update_model_usage(model_path)

            logger.info(f"Model loaded successfully")
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise ValueError(f"Failed to load model: {str(e)}")

    def _update_model_usage(self, model_path: str) -> None:
        """
        Update usage statistics for the model.

        Args:
            model_path (str): Path to the model file.
        """
        try:
            # Skip for best_model.h5 which is a symlink
            if os.path.basename(model_path) == "best_model.h5":
                return

            # Find metadata file
            metadata_path = model_path.replace(".h5", "_metadata.json")

            if os.path.exists(metadata_path):
                # Load existing metadata
                with open(metadata_path, "r") as f:
                    metadata = json.load(f)

                # Update usage statistics
                metadata["last_used"] = datetime.now().strftime("%Y%m%d_%H%M%S")
                metadata["usage_count"] = metadata.get("usage_count", 0) + 1

                # Save updated metadata
                with open(metadata_path, "w") as f:
                    json.dump(metadata, f, indent=2)
            else:
                # Create new metadata file
                metadata = {
                    "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
                    "last_used": datetime.now().strftime("%Y%m%d_%H%M%S"),
                    "usage_count": 1,
                    "metrics": {}
                }

                with open(metadata_path, "w") as f:
                    json.dump(metadata, f, indent=2)
        except Exception as e:
            logger.warning(f"Error updating model usage statistics: {str(e)}")

    def predict(self, market_data: List[Dict[str, Any]]) -> Tuple[int, float]:
        """
        Make a prediction based on the provided market data.

        Args:
            market_data (List[Dict[str, Any]]): List of market data points.
                Each point should be a dictionary with keys:
                - timestamp: Unix timestamp in milliseconds
                - open: Opening price
                - high: Highest price
                - low: Lowest price
                - close: Closing price
                - volume: Trading volume
                - num_trades: Number of trades (optional)

        Returns:
            Tuple[int, float]: Prediction class (0: down, 1: up) and probability.

        Raises:
            ValueError: If the model is not loaded or if the market data is invalid.
        """
        if self.model is None:
            raise ValueError("Model not loaded. Call load_model() first.")

        try:
            # Convert market data to DataFrame
            df = pd.DataFrame(market_data)

            # Convert timestamps to datetime
            df['date_timestamp'] = [datetime.utcfromtimestamp(int(ts/1000))
                                   for ts in df['timestamp']]

            # Process data for prediction
            features = self.data_processor.process_data(df)

            # Make prediction
            prediction = self.model.predict(features)

            # Get prediction class and probability
            if prediction.shape[1] == 2:  # One-hot encoded output
                pred_class = np.argmax(prediction[0])
                pred_prob = prediction[0, pred_class]
            else:  # Single output
                pred_class = int(prediction[0, 0] > 0.5)
                pred_prob = float(prediction[0, 0] if pred_class == 1 else 1 - prediction[0, 0])

            logger.info(f"Prediction: class={pred_class}, probability={pred_prob:.4f}")

            return pred_class, pred_prob

        except Exception as e:
            logger.error(f"Prediction error: {str(e)}")
            raise ValueError(f"Failed to make prediction: {str(e)}")

    def evaluate(self, market_data: List[Dict[str, Any]], labels: List[int]) -> Dict[str, float]:
        """
        Evaluate the model on the provided market data and labels.

        Args:
            market_data (List[Dict[str, Any]]): List of market data points.
            labels (List[int]): List of true labels.

        Returns:
            Dict[str, float]: Evaluation metrics.

        Raises:
            ValueError: If the model is not loaded or if the data is invalid.
        """
        if self.model is None:
            raise ValueError("Model not loaded. Call load_model() first.")

        try:
            # Convert market data to DataFrame
            df = pd.DataFrame(market_data)

            # Convert timestamps to datetime
            df['date_timestamp'] = [datetime.utcfromtimestamp(int(ts/1000))
                                   for ts in df['timestamp']]

            # Process data for evaluation
            features = self.data_processor.process_data(df)

            # Convert labels to one-hot encoding if needed
            if self.model.output_shape[1] == 2:  # One-hot encoded output
                from tensorflow.keras.utils import to_categorical
                labels = to_categorical(labels, num_classes=2)

            # Evaluate model
            metrics = self.model.evaluate(features, np.array(labels), verbose=0)

            # Create metrics dictionary
            metrics_dict = {
                "loss": metrics[0],
                "accuracy": metrics[1]
            }

            if len(metrics) > 2:
                metrics_dict["f1_score"] = metrics[2]

            logger.info(f"Evaluation metrics: {metrics_dict}")

            return metrics_dict

        except Exception as e:
            logger.error(f"Evaluation error: {str(e)}")
            raise ValueError(f"Failed to evaluate model: {str(e)}")
