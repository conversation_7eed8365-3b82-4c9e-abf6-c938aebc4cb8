#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Trainer module for the Trend Prediction Service.
This module provides functionality for training and evaluating models.
"""

import os
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Union, Any
from datetime import datetime
import json

import tensorflow as tf
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau
from sklearn.metrics import accuracy_score, f1_score

from app.utils.data_processor import DataProcessor
from app.utils.model_builder import create_cnn_model
from app.utils.config import settings
from app.utils.training_callbacks import TrainingProgressCallback

# Import training status management functions
try:
    from app.api.training import register_training_session, update_training_status, remove_training_session
except ImportError:
    # Define dummy functions for when the API is not available
    def register_training_session(pair, status): pass
    def update_training_status(pair, status): pass
    def remove_training_session(pair): pass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class ModelTrainer:
    """
    Class for training and evaluating CNN models.
    """

    def __init__(self, pair: str = None):
        """Initialize the trainer.

        Args:
            pair (str, optional): Trading pair symbol (e.g., "BTCUSDT").
                If None, the default pair will be used.
        """
        self.data_processor = DataProcessor()
        self.pair = pair or settings.DEFAULT_PAIR

        # Check if pair is supported
        if not settings.is_pair_supported(self.pair):
            logger.warning(f"Pair {self.pair} is not supported")

        # Define callbacks with more patience for better performance
        self.early_stopping = EarlyStopping(
            monitor='val_loss',
            mode='min',
            verbose=1,
            patience=50,  # Increased from 20 to 50
            min_delta=0.0001
        )

        self.reduce_lr = ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.1,  # Changed from 0.02 to 0.1 for better learning rate reduction
            patience=10,  # Increased from 5 to 10
            verbose=1,
            mode='min',
            min_delta=0.001,
            cooldown=1,
            min_lr=0.00001  # Reduced minimum learning rate
        )

        # Training status
        self.training_status = {
            "is_training": False,
            "progress": 0.0,
            "current_epoch": 0,
            "total_epochs": 0,
            "metrics": {},
            "start_time": None,
            "end_time": None,
            "error": None
        }

    def train(self, data_source: Dict[str, Any], params: Dict[str, Any], save_path: str = None, pair: str = None) -> Dict[str, Any]:
        """
        Train a new model with the provided data.

        Args:
            data_source (Dict[str, Any]): Source of training data.
                Either {'market_data': List[Dict]} or {'data_path': str}.
            params (Dict[str, Any]): Training parameters.
                Should include:
                - epochs: Number of training epochs
                - batch_size: Batch size for training
                - learning_rate: Learning rate for the optimizer
            save_path (str, optional): Directory to save the trained model.
                If None, uses the default directory for the specified pair.
            pair (str, optional): Trading pair symbol (e.g., "BTCUSDT").
                If None, uses the pair specified during initialization.

        Returns:
            Dict[str, Any]: Training results including model path and metrics.

        Raises:
            ValueError: If the data source is invalid or training fails.
        """
        try:
            # Determine pair
            active_pair = pair or self.pair

            # Reset training status
            self.training_status = {
                "is_training": True,
                "progress": 0.0,
                "current_epoch": 0,
                "total_epochs": params.get('epochs', 64),
                "metrics": {},
                "start_time": datetime.now().strftime("%Y%m%d_%H%M%S"),
                "end_time": None,
                "error": None,
                "pair": active_pair
            }

            # Register training session with the API
            register_training_session(active_pair, self.training_status)

            # Check if pair is supported
            if not settings.is_pair_supported(active_pair):
                error_msg = f"Pair {active_pair} is not supported"
                self.training_status["error"] = error_msg
                raise ValueError(error_msg)

            # Determine save path
            if save_path is None:
                save_path = settings.get_pair_model_dir(active_pair)

            # Ensure save directory exists
            os.makedirs(save_path, exist_ok=True)

            # Load data
            if 'market_data' in data_source:
                # Convert market data to DataFrame
                df = pd.DataFrame(data_source['market_data'])

                # Convert timestamps to datetime
                df['date_timestamp'] = [datetime.utcfromtimestamp(int(ts/1000))
                                       for ts in df['timestamp']]
            elif 'data_path' in data_source:
                # Load data from CSV file
                df = pd.read_csv(data_source['data_path'])

                # Convert timestamps to datetime if needed
                if 'timestamp' in df.columns and 'date_timestamp' not in df.columns:
                    df['date_timestamp'] = [datetime.utcfromtimestamp(int(ts/1000))
                                           for ts in df['timestamp']]
            else:
                raise ValueError("Invalid data source. Must provide either 'market_data' or 'data_path'.")

            # Process data for training
            x_train, x_validation, y_train, y_validation, labels = self.data_processor.prepare_training_data(df)

            # Create model
            model = create_cnn_model(
                params={
                    'batch_size': params.get('batch_size', 80),
                    'epochs': params.get('epochs', 64),
                    'lr': params.get('learning_rate', 0.001),
                    'optimizer': params.get('optimizer', 'adam'),
                    'conv2d_layers': {
                        'conv2d_drop_out_1': 0.22,
                        'filters_1': 20,
                        'kernel_size_1': 2,
                        'conv2d_mp_1': 2,
                        'strides_1': 1,
                        'kernel_regularizer_1': 0.0,
                        'conv2_drop_out_2': 0.05,
                        'filters_2': 40,
                        'kernel_size_2': 2,
                        'conv2d_mp_2': 2,
                        'strides_2': 1,
                        'kernel_regularizer_2': 0.0
                    },
                    'dense_layers': {
                        'dense_drop_out_1': 0.22,
                        'dense_nodes_1': 32,
                        'kernel_regularizer_1': 0.0
                    }
                },
                x_train=x_train,
                labels=labels,
                multiclass=False
            )

            # Define model checkpoint callback
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_filename = f"{active_pair}_model_{timestamp}.h5"
            model_path = os.path.join(save_path, model_filename)

            # Update training status
            self.training_status["model_path"] = model_path
            self.training_status["pair"] = active_pair

            checkpoint = ModelCheckpoint(
                filepath=model_path,
                monitor='val_loss',
                save_best_only=True,
                verbose=1
            )

            # Create progress callback
            progress_callback = TrainingProgressCallback(self.training_status)

            # Train model
            history = model.fit(
                x_train, y_train,
                validation_data=(x_validation, y_validation),
                epochs=params.get('epochs', 64),
                batch_size=params.get('batch_size', 80),
                callbacks=[self.early_stopping, self.reduce_lr, checkpoint, progress_callback],
                verbose=1
            )

            # Update training status
            self.training_status["is_training"] = False
            self.training_status["progress"] = 1.0
            self.training_status["end_time"] = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Update training status in the API
            update_training_status(active_pair, self.training_status)

            # Evaluate model
            y_pred = model.predict(x_validation)
            y_pred_classes = np.argmax(y_pred, axis=1)
            y_true = np.argmax(y_validation, axis=1)

            accuracy = accuracy_score(y_true, y_pred_classes)
            f1 = f1_score(y_true, y_pred_classes)

            # Save training history
            history_filename = f"{active_pair}_history_{timestamp}.json"
            history_path = os.path.join(save_path, history_filename)
            with open(history_path, 'w') as f:
                history_dict = {key: [float(val) for val in values] for key, values in history.history.items()}
                json.dump(history_dict, f)

            # Save model metadata
            metadata_filename = model_filename.replace(".h5", "_metadata.json")
            metadata_path = os.path.join(save_path, metadata_filename)
            metadata = {
                "timestamp": timestamp,
                "pair": active_pair,
                "params": params,
                "metrics": {
                    "accuracy": float(accuracy),
                    "f1_score": float(f1),
                    "val_loss": float(min(history.history['val_loss']))
                },
                "model_path": model_path,
                "history_path": history_path,
                "last_used": timestamp,
                "usage_count": 0
            }

            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)

            logger.info(f"Model trained and saved to {model_path}")
            logger.info(f"Accuracy: {accuracy:.4f}, F1 Score: {f1:.4f}")

            # Compare with existing models
            best_model = self.compare_models(save_path, metadata)

            return {
                "model_path": model_path,
                "metrics": {
                    "accuracy": float(accuracy),
                    "f1_score": float(f1),
                    "val_loss": float(min(history.history['val_loss']))
                },
                "best_model": best_model
            }

        except Exception as e:
            logger.error(f"Training error: {str(e)}")

            # Update training status with error
            self.training_status["is_training"] = False
            self.training_status["error"] = str(e)
            self.training_status["end_time"] = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Update training status in the API
            if 'pair' in self.training_status:
                update_training_status(self.training_status['pair'], self.training_status)

            # Remove training session after a delay
            if 'pair' in self.training_status:
                try:
                    remove_training_session(self.training_status['pair'])
                except Exception as remove_error:
                    logger.warning(f"Failed to remove training session: {str(remove_error)}")

            raise ValueError(f"Failed to train model: {str(e)}")

    def compare_models(self, models_dir: str, current_model: Dict[str, Any], pair: str = None) -> Dict[str, Any]:
        """
        Compare the current model with existing models and identify the best one.

        Args:
            models_dir (str): Directory containing model files.
            current_model (Dict[str, Any]): Metadata of the current model.
            pair (str, optional): Trading pair symbol (e.g., "BTCUSDT").
                If None, uses the pair from the current model.

        Returns:
            Dict[str, Any]: Metadata of the best model.
        """
        try:
            # Determine pair
            active_pair = pair or current_model.get('pair') or self.pair

            # Find all metadata files for this pair
            metadata_files = [f for f in os.listdir(models_dir)
                             if f.startswith(f"{active_pair}_model_") and f.endswith('_metadata.json')]

            if not metadata_files:
                logger.info(f"No existing models to compare with for pair {active_pair}.")
                return current_model

            # Load metadata for all models
            models = []
            for metadata_file in metadata_files:
                try:
                    with open(os.path.join(models_dir, metadata_file), 'r') as f:
                        metadata = json.load(f)
                        models.append(metadata)
                except Exception as e:
                    logger.warning(f"Failed to load metadata from {metadata_file}: {str(e)}")

            # Add current model if not already in the list
            current_model_path = current_model.get('model_path', '')
            if not any(model.get('model_path') == current_model_path for model in models):
                models.append(current_model)

            # Find the best model based on validation loss
            best_model = min(models, key=lambda x: x['metrics']['val_loss'])

            # Check if the current model is the best
            if best_model['model_path'] == current_model['model_path']:
                logger.info(f"Current model is the best model for {active_pair}.")
            else:
                logger.info(f"Best model for {active_pair} is {os.path.basename(best_model['model_path'])} with validation loss {best_model['metrics']['val_loss']:.4f}")

            # Create a symlink to the best model
            best_model_link = os.path.join(models_dir, f"{active_pair}_best_model.h5")
            if os.path.exists(best_model_link):
                if os.path.islink(best_model_link):
                    os.remove(best_model_link)
                else:
                    # Rename the file instead of removing it
                    os.rename(best_model_link, f"{best_model_link}.bak")

            # Create symlink to the best model
            os.symlink(best_model['model_path'], best_model_link)
            logger.info(f"Created symlink to best model for {active_pair}: {best_model_link}")

            # Also update the generic best_model.h5 symlink in the pair directory
            generic_best_link = os.path.join(models_dir, 'best_model.h5')
            if os.path.exists(generic_best_link):
                if os.path.islink(generic_best_link):
                    os.remove(generic_best_link)
                else:
                    # Rename the file instead of removing it
                    os.rename(generic_best_link, f"{generic_best_link}.bak")

            os.symlink(best_model['model_path'], generic_best_link)

            return best_model

        except Exception as e:
            logger.error(f"Error comparing models: {str(e)}")
            return current_model
