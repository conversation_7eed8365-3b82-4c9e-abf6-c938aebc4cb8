#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for the data processor module.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from app.utils.data_processor import DataProcessor


class TestDataProcessor(unittest.TestCase):
    """Test cases for the data processor module."""

    def setUp(self):
        """Set up test fixtures."""
        self.data_processor = DataProcessor()
        
        # Create sample market data
        dates = [datetime.now() - timedelta(hours=i) for i in range(100)]
        self.sample_data = pd.DataFrame({
            'timestamp': [int(d.timestamp() * 1000) for d in dates],
            'date_timestamp': dates,
            'open': np.random.uniform(9000, 10000, 100),
            'high': np.random.uniform(10000, 11000, 100),
            'low': np.random.uniform(8000, 9000, 100),
            'close': np.random.uniform(9500, 10500, 100),
            'volume': np.random.uniform(1000, 2000, 100),
            'num_trades': np.random.randint(100, 1000, 100)
        })

    def test_process_data(self):
        """Test that data processing works correctly."""
        processed_data = self.data_processor.process_data(self.sample_data)
        
        # Check that the output has the right shape and type
        self.assertIsInstance(processed_data, np.ndarray)
        self.assertEqual(len(processed_data.shape), 4)  # (samples, height, width, channels)
        self.assertEqual(processed_data.shape[3], 3)  # RGB channels
        
        # Check that the number of samples matches the input
        self.assertGreater(processed_data.shape[0], 0)

    def test_prepare_training_data(self):
        """Test that training data preparation works correctly."""
        x_train, x_val, y_train, y_val, labels = self.data_processor.prepare_training_data(self.sample_data)
        
        # Check that the outputs have the right shapes and types
        self.assertIsInstance(x_train, np.ndarray)
        self.assertIsInstance(x_val, np.ndarray)
        self.assertIsInstance(y_train, np.ndarray)
        self.assertIsInstance(y_val, np.ndarray)
        self.assertIsInstance(labels, np.ndarray)
        
        # Check that x_train and x_val have the right dimensions
        self.assertEqual(len(x_train.shape), 4)  # (samples, height, width, channels)
        self.assertEqual(len(x_val.shape), 4)  # (samples, height, width, channels)
        
        # Check that y_train and y_val have the right dimensions
        self.assertEqual(len(y_train.shape), 2)  # (samples, classes)
        self.assertEqual(len(y_val.shape), 2)  # (samples, classes)
        
        # Check that the number of classes is correct
        self.assertEqual(y_train.shape[1], len(labels))
        self.assertEqual(y_val.shape[1], len(labels))
        
        # Check that the train and validation sets have the right proportions
        total_samples = x_train.shape[0] + x_val.shape[0]
        self.assertAlmostEqual(x_val.shape[0] / total_samples, 0.2, delta=0.05)  # 20% validation

    def test_transform_to_image(self):
        """Test that feature transformation to image format works correctly."""
        # Create sample features
        features = np.random.random((10, 16))  # 10 samples, 16 features
        
        # Transform to image
        images = self.data_processor._transform_to_image(features)
        
        # Check that the output has the right shape
        self.assertEqual(images.shape, (10, 4, 4, 3))  # 10 samples, 4x4 images, 3 channels


if __name__ == "__main__":
    unittest.main()
