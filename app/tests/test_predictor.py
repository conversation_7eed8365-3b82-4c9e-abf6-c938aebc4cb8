#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for the predictor module.
"""

import unittest
from unittest.mock import patch, MagicMock
import os
import numpy as np
import tensorflow as tf
from datetime import datetime

from app.models.predictor import TrendPredictor


class TestPredictor(unittest.TestCase):
    """Test cases for the predictor module."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock model
        self.mock_model = MagicMock()
        self.mock_model.predict.return_value = np.array([[0.7, 0.3]])
        
        # Create sample market data
        self.sample_market_data = [
            {
                "timestamp": 1609459200000,
                "open": 29000.0,
                "high": 29300.0,
                "low": 28800.0,
                "close": 29100.0,
                "volume": 1000.0,
                "num_trades": 100,
                "symbol": "BTCUSDT"
            },
            {
                "timestamp": 1609455600000,
                "open": 28900.0,
                "high": 29100.0,
                "low": 28700.0,
                "close": 29000.0,
                "volume": 900.0,
                "num_trades": 90,
                "symbol": "BTCUSDT"
            }
        ]

    @patch('app.models.predictor.load_model')
    @patch('app.models.predictor.os.path.exists')
    def test_init_with_model_path(self, mock_exists, mock_load_model):
        """Test that the predictor is initialized correctly with a model path."""
        # Mock the exists and load_model functions
        mock_exists.return_value = True
        mock_load_model.return_value = self.mock_model
        
        # Initialize the predictor
        predictor = TrendPredictor(model_path="models/BTCUSDT/best_model.h5", pair="BTCUSDT")
        
        # Check that the model was loaded
        mock_load_model.assert_called_once_with("models/BTCUSDT/best_model.h5", compile=False)
        self.assertEqual(predictor.model, self.mock_model)
        self.assertEqual(predictor.pair, "BTCUSDT")

    @patch('app.models.predictor.load_model')
    @patch('app.models.predictor.os.path.exists')
    def test_load_model(self, mock_exists, mock_load_model):
        """Test that a model is loaded correctly."""
        # Mock the exists and load_model functions
        mock_exists.return_value = True
        mock_load_model.return_value = self.mock_model
        
        # Initialize the predictor without a model
        predictor = TrendPredictor(pair="BTCUSDT")
        
        # Load a model
        predictor.load_model("models/BTCUSDT/model_20230101_120000.h5")
        
        # Check that the model was loaded
        mock_load_model.assert_called_once_with("models/BTCUSDT/model_20230101_120000.h5", compile=False)
        self.assertEqual(predictor.model, self.mock_model)
        self.assertEqual(predictor.model_path, "models/BTCUSDT/model_20230101_120000.h5")

    @patch('app.models.predictor.DataProcessor.process_data')
    def test_predict(self, mock_process_data):
        """Test that predictions are made correctly."""
        # Mock the process_data method
        mock_process_data.return_value = np.random.random((2, 10, 10, 3))
        
        # Initialize the predictor with a mock model
        predictor = TrendPredictor(pair="BTCUSDT")
        predictor.model = self.mock_model
        
        # Make a prediction
        prediction, probability = predictor.predict(self.sample_market_data)
        
        # Check that the prediction is correct
        self.assertEqual(prediction, 0)  # Class 0 has higher probability
        self.assertEqual(probability, 0.7)  # Probability of class 0
        
        # Check that the model's predict method was called
        self.mock_model.predict.assert_called_once()

    @patch('app.models.predictor.DataProcessor.process_data')
    def test_predict_with_single_output(self, mock_process_data):
        """Test that predictions are made correctly with a single output model."""
        # Mock the process_data method
        mock_process_data.return_value = np.random.random((2, 10, 10, 3))
        
        # Create a mock model with single output
        mock_model = MagicMock()
        mock_model.predict.return_value = np.array([[0.7]])
        
        # Initialize the predictor with the mock model
        predictor = TrendPredictor(pair="BTCUSDT")
        predictor.model = mock_model
        
        # Make a prediction
        prediction, probability = predictor.predict(self.sample_market_data)
        
        # Check that the prediction is correct
        self.assertEqual(prediction, 1)  # Output > 0.5 means class 1
        self.assertEqual(probability, 0.7)  # Probability of class 1
        
        # Check that the model's predict method was called
        mock_model.predict.assert_called_once()


if __name__ == "__main__":
    unittest.main()
