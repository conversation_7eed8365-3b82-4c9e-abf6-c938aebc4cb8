#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for the training callbacks module.
"""

import unittest
from unittest.mock import patch, MagicMock
import numpy as np

from app.utils.training_callbacks import TrainingProgressCallback


class TestTrainingCallbacks(unittest.TestCase):
    """Test cases for the training callbacks module."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a training status dictionary
        self.training_status = {
            "is_training": False,
            "progress": 0.0,
            "current_epoch": 0,
            "total_epochs": 10,
            "metrics": {},
            "start_time": None,
            "end_time": None,
            "error": None
        }
        
        # Create a callback
        self.callback = TrainingProgressCallback(self.training_status)
        
        # Create sample logs
        self.sample_logs = {
            "loss": 0.5,
            "accuracy": 0.8,
            "val_loss": 0.6,
            "val_accuracy": 0.7
        }

    def test_on_epoch_begin(self):
        """Test that on_epoch_begin updates the training status correctly."""
        # Call on_epoch_begin
        self.callback.on_epoch_begin(5)
        
        # Check that the training status was updated
        self.assertEqual(self.training_status["current_epoch"], 6)  # 0-based to 1-based
        self.assertEqual(self.training_status["progress"], 0.5)  # 5/10

    def test_on_epoch_end(self):
        """Test that on_epoch_end updates the training status correctly."""
        # Call on_epoch_end
        self.callback.on_epoch_end(5, self.sample_logs)
        
        # Check that the training status was updated
        self.assertEqual(self.training_status["progress"], 0.6)  # (5+1)/10
        self.assertEqual(self.training_status["metrics"]["loss"], 0.5)
        self.assertEqual(self.training_status["metrics"]["accuracy"], 0.8)
        self.assertEqual(self.training_status["metrics"]["val_loss"], 0.6)
        self.assertEqual(self.training_status["metrics"]["val_accuracy"], 0.7)

    def test_on_train_begin(self):
        """Test that on_train_begin updates the training status correctly."""
        # Call on_train_begin
        self.callback.on_train_begin()
        
        # Check that the training status was updated
        self.assertTrue(self.training_status["is_training"])
        self.assertEqual(self.training_status["progress"], 0.0)
        self.assertIsNone(self.training_status["error"])

    @patch('app.utils.training_callbacks.datetime')
    def test_on_train_end(self, mock_datetime):
        """Test that on_train_end updates the training status correctly."""
        # Mock the datetime
        mock_datetime.now.return_value = MagicMock(strftime=MagicMock(return_value="20230101_120000"))
        
        # Call on_train_end
        self.callback.on_train_end()
        
        # Check that the training status was updated
        self.assertFalse(self.training_status["is_training"])
        self.assertEqual(self.training_status["progress"], 1.0)
        self.assertEqual(self.training_status["end_time"], "20230101_120000")


if __name__ == "__main__":
    unittest.main()
