#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for the model builder module.
"""

import unittest
import numpy as np
import tensorflow as tf

from app.utils.model_builder import create_cnn_model


class TestModelBuilder(unittest.TestCase):
    """Test cases for the model builder module."""

    def setUp(self):
        """Set up test fixtures."""
        # Create sample data
        self.x_train = np.random.random((100, 10, 10, 3))  # 100 samples, 10x10 images, 3 channels
        self.labels = np.array([0, 1])  # Binary classification
        
        # Define model parameters
        self.params = {
            'conv2d_layers': {
                'filters_1': 32,
                'kernel_size_1': 3,
                'strides_1': 1,
                'kernel_regularizer_1': 0.01,
                'conv2d_drop_out_1': 0.2,
                'filters_2': 64,
                'kernel_size_2': 3,
                'strides_2': 1,
                'kernel_regularizer_2': 0.01,
                'conv2_drop_out_2': 0.2
            },
            'dense_layers': {
                'dense_nodes_1': 64,
                'kernel_regularizer_1': 0.01,
                'dense_drop_out_1': 0.2
            },
            'optimizer': 'adam',
            'lr': 0.001
        }

    def test_create_cnn_model_binary(self):
        """Test that a binary classification CNN model is created correctly."""
        model = create_cnn_model(self.params, self.x_train, self.labels)
        
        # Check that the model is a Keras model
        self.assertIsInstance(model, tf.keras.Model)
        
        # Check that the model has the right input shape
        self.assertEqual(model.input_shape, (None, 10, 10, 3))
        
        # Check that the model has the right output shape
        self.assertEqual(model.output_shape, (None, 2))
        
        # Check that the model is compiled
        self.assertIsNotNone(model.optimizer)
        self.assertIsNotNone(model.loss)
        self.assertIsNotNone(model.metrics)

    def test_create_cnn_model_multiclass(self):
        """Test that a multiclass CNN model is created correctly."""
        # Create multiclass labels
        multiclass_labels = np.array([0, 1, 2])
        
        model = create_cnn_model(self.params, self.x_train, multiclass_labels, multiclass=True)
        
        # Check that the model is a Keras model
        self.assertIsInstance(model, tf.keras.Model)
        
        # Check that the model has the right input shape
        self.assertEqual(model.input_shape, (None, 10, 10, 3))
        
        # Check that the model has the right output shape
        self.assertEqual(model.output_shape, (None, 3))
        
        # Check that the model is compiled
        self.assertIsNotNone(model.optimizer)
        self.assertIsNotNone(model.loss)
        self.assertIsNotNone(model.metrics)

    def test_create_cnn_model_different_optimizers(self):
        """Test that models with different optimizers are created correctly."""
        # Test RMSprop
        params = self.params.copy()
        params['optimizer'] = 'rmsprop'
        model_rmsprop = create_cnn_model(params, self.x_train, self.labels)
        self.assertIsInstance(model_rmsprop.optimizer, tf.keras.optimizers.RMSprop)
        
        # Test SGD
        params['optimizer'] = 'sgd'
        model_sgd = create_cnn_model(params, self.x_train, self.labels)
        self.assertIsInstance(model_sgd.optimizer, tf.keras.optimizers.SGD)
        
        # Test Adam
        params['optimizer'] = 'adam'
        model_adam = create_cnn_model(params, self.x_train, self.labels)
        self.assertIsInstance(model_adam.optimizer, tf.keras.optimizers.Adam)


if __name__ == "__main__":
    unittest.main()
