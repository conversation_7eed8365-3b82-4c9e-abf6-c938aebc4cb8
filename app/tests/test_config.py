#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for the config module.
"""

import os
import unittest
from unittest.mock import patch

from app.utils.config import Settings


class TestConfig(unittest.TestCase):
    """Test cases for the config module."""

    def test_default_settings(self):
        """Test that default settings are loaded correctly."""
        settings = Settings()
        self.assertEqual(settings.API_TITLE, "Trend Prediction Service")
        self.assertEqual(settings.API_VERSION, "1.0.0")
        self.assertEqual(settings.MODEL_DIR, "models")
        self.assertEqual(settings.DEFAULT_PAIR, "BTCUSDT")
        self.assertEqual(settings.DATA_DIR, "data")
        self.assertEqual(settings.LOG_LEVEL, "INFO")
        self.assertEqual(settings.MODEL_MAX_AGE_DAYS, 90)
        self.assertEqual(settings.MODEL_MAX_VERSIONS, 5)
        self.assertTrue(settings.MODEL_CLEANUP_ENABLED)
        self.assertEqual(settings.MAX_CONCURRENT_TRAININGS, 2)
        self.assertIn("BTCUSDT", settings.SUPPORTED_PAIRS)

    @patch.dict(os.environ, {"MODEL_DIR": "custom_models"})
    def test_environment_override(self):
        """Test that environment variables override default settings."""
        settings = Settings()
        self.assertEqual(settings.MODEL_DIR, "custom_models")

    def test_get_pair_model_dir(self):
        """Test that pair model directory is constructed correctly."""
        settings = Settings()
        self.assertEqual(settings.get_pair_model_dir("BTCUSDT"), "models/BTCUSDT")
        self.assertEqual(settings.get_pair_model_dir("ETHUSDT"), "models/ETHUSDT")

    def test_get_default_model_path(self):
        """Test that default model path is constructed correctly."""
        settings = Settings()
        self.assertEqual(settings.get_default_model_path("BTCUSDT"), "models/BTCUSDT/best_model.h5")
        self.assertEqual(settings.get_default_model_path("ETHUSDT"), "models/ETHUSDT/best_model.h5")

    def test_is_pair_supported(self):
        """Test that pair support is checked correctly."""
        settings = Settings()
        self.assertTrue(settings.is_pair_supported("BTCUSDT"))
        self.assertFalse(settings.is_pair_supported("UNKNOWN"))


if __name__ == "__main__":
    unittest.main()
