#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for the API endpoints.
"""

import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
import json
import os
import numpy as np

from app.api.main import app


class TestAPI(unittest.TestCase):
    """Test cases for the API endpoints."""

    def setUp(self):
        """Set up test fixtures."""
        self.client = TestClient(app)
        
        # Create sample market data
        self.sample_market_data = [
            {
                "timestamp": 1609459200000,
                "open": 29000.0,
                "high": 29300.0,
                "low": 28800.0,
                "close": 29100.0,
                "volume": 1000.0,
                "num_trades": 100,
                "symbol": "BTCUSDT"
            },
            {
                "timestamp": 1609455600000,
                "open": 28900.0,
                "high": 29100.0,
                "low": 28700.0,
                "close": 29000.0,
                "volume": 900.0,
                "num_trades": 90,
                "symbol": "BTCUSDT"
            }
        ]

    @patch('app.models.predictor.TrendPredictor.predict')
    def test_predict_endpoint(self, mock_predict):
        """Test that the predict endpoint works correctly."""
        # Mock the predict method
        mock_predict.return_value = (1, 0.85)
        
        # Make a request to the predict endpoint
        response = self.client.post(
            "/predict",
            json={"market_data": self.sample_market_data}
        )
        
        # Check that the response is correct
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["prediction"], 1)
        self.assertEqual(data["probability"], 0.85)
        self.assertEqual(data["symbol"], "BTCUSDT")

    @patch('app.models.trainer.ModelTrainer.train')
    def test_train_endpoint(self, mock_train):
        """Test that the train endpoint works correctly."""
        # Mock the train method
        mock_train.return_value = {
            "model_path": "models/BTCUSDT/model_20230101_120000.h5",
            "metrics": {"accuracy": 0.75, "f1_score": 0.8}
        }
        
        # Make a request to the train endpoint
        response = self.client.post(
            "/train",
            json={
                "market_data": self.sample_market_data,
                "epochs": 10,
                "batch_size": 32,
                "learning_rate": 0.001,
                "pair": "BTCUSDT"
            }
        )
        
        # Check that the response is correct
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["status"], "Training started in the background")
        self.assertEqual(data["pair"], "BTCUSDT")
        self.assertEqual(data["progress"], 0.0)

    @patch('app.models.backtester.ModelBacktester.backtest')
    def test_backtest_endpoint(self, mock_backtest):
        """Test that the backtest endpoint works correctly."""
        # Mock the backtest method
        mock_backtest.return_value = {
            "metrics": {
                "accuracy": 0.75,
                "precision": 0.8,
                "recall": 0.7,
                "f1_score": 0.75,
                "auc": 0.85
            },
            "trading_metrics": {
                "win_rate": 0.65,
                "profit_factor": 1.5,
                "max_drawdown": 0.2,
                "sharpe_ratio": 1.2
            },
            "plots": {}
        }
        
        # Make a request to the backtest endpoint
        response = self.client.post(
            "/backtest",
            json={
                "model_path": "models/BTCUSDT/best_model.h5",
                "data_path": "data/BTCUSDT_1h.csv",
                "window": 24,
                "generate_plots": True
            }
        )
        
        # Check that the response is correct
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["metrics"]["accuracy"], 0.75)
        self.assertEqual(data["trading_metrics"]["win_rate"], 0.65)

    def test_models_endpoint(self):
        """Test that the models endpoint works correctly."""
        # Make a request to the models endpoint
        response = self.client.get("/models")
        
        # Check that the response is correct
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIsInstance(data, dict)

    def test_training_status_endpoint(self):
        """Test that the training status endpoint works correctly."""
        # Make a request to the training status endpoint
        response = self.client.get("/training/status")
        
        # Check that the response is correct
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIsInstance(data, dict)


if __name__ == "__main__":
    unittest.main()
