#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for the backtester module.
"""

import unittest
from unittest.mock import patch, MagicMock
import os
import numpy as np
import pandas as pd
from datetime import datetime

from app.models.backtester import ModelBacktester


class TestBacktester(unittest.TestCase):
    """Test cases for the backtester module."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock predictor
        self.mock_predictor = MagicMock()
        self.mock_predictor.predict.side_effect = [(1, 0.8), (0, 0.7), (1, 0.9)]
        
        # Create sample data
        dates = [datetime.now().timestamp() * 1000 - i * 3600000 for i in range(100)]
        self.sample_data = pd.DataFrame({
            'timestamp': dates,
            'open': np.random.uniform(9000, 10000, 100),
            'high': np.random.uniform(10000, 11000, 100),
            'low': np.random.uniform(8000, 9000, 100),
            'close': np.random.uniform(9500, 10500, 100),
            'volume': np.random.uniform(1000, 2000, 100),
            'num_trades': np.random.randint(100, 1000, 100)
        })
        
        # Add target column
        self.sample_data['target'] = np.random.randint(0, 2, 100)

    @patch('app.models.backtester.TrendPredictor')
    @patch('app.models.backtester.pd.read_csv')
    def test_backtest_with_data_path(self, mock_read_csv, mock_predictor_class):
        """Test that backtesting works correctly with a data path."""
        # Mock the dependencies
        mock_read_csv.return_value = self.sample_data
        mock_predictor_class.return_value = self.mock_predictor
        
        # Initialize the backtester
        backtester = ModelBacktester()
        
        # Backtest the model
        result = backtester.backtest(
            model_path="models/BTCUSDT/best_model.h5",
            data_source={"data_path": "data/BTCUSDT_1h.csv"},
            window=24,
            generate_plots=False
        )
        
        # Check that the result contains the expected keys
        self.assertIn("metrics", result)
        self.assertIn("trading_metrics", result)
        self.assertIn("predictions", result)
        
        # Check that the metrics contain the expected keys
        self.assertIn("accuracy", result["metrics"])
        self.assertIn("precision", result["metrics"])
        self.assertIn("recall", result["metrics"])
        self.assertIn("f1_score", result["metrics"])
        
        # Check that the trading metrics contain the expected keys
        self.assertIn("win_rate", result["trading_metrics"])
        self.assertIn("profit_factor", result["trading_metrics"])
        self.assertIn("max_drawdown", result["trading_metrics"])
        self.assertIn("sharpe_ratio", result["trading_metrics"])

    @patch('app.models.backtester.TrendPredictor')
    def test_backtest_with_market_data(self, mock_predictor_class):
        """Test that backtesting works correctly with market data."""
        # Mock the dependencies
        mock_predictor_class.return_value = self.mock_predictor
        
        # Initialize the backtester
        backtester = ModelBacktester()
        
        # Backtest the model with market data
        result = backtester.backtest(
            model_path="models/BTCUSDT/best_model.h5",
            data_source={"market_data": self.sample_data.to_dict('records')},
            window=24,
            generate_plots=False
        )
        
        # Check that the result contains the expected keys
        self.assertIn("metrics", result)
        self.assertIn("trading_metrics", result)
        self.assertIn("predictions", result)

    @patch('app.models.backtester.TrendPredictor')
    @patch('app.models.backtester.pd.read_csv')
    def test_compare_models(self, mock_read_csv, mock_predictor_class):
        """Test that model comparison works correctly."""
        # Mock the dependencies
        mock_read_csv.return_value = self.sample_data
        mock_predictor_class.return_value = self.mock_predictor
        
        # Initialize the backtester
        backtester = ModelBacktester()
        
        # Compare models
        result = backtester.compare_models(
            model_paths=[
                "models/BTCUSDT/model_20230101_120000.h5",
                "models/BTCUSDT/model_20230201_120000.h5"
            ],
            data_source={"data_path": "data/BTCUSDT_1h.csv"},
            window=24
        )
        
        # Check that the result contains the expected keys
        self.assertIn("models", result)
        self.assertIn("best_model", result)
        
        # Check that the models list contains results for each model
        self.assertEqual(len(result["models"]), 2)
        
        # Check that each model result contains the expected keys
        for model_result in result["models"]:
            self.assertIn("model_path", model_result)
            self.assertIn("metrics", model_result)
            self.assertIn("trading_metrics", model_result)


if __name__ == "__main__":
    unittest.main()
