#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for the indicators module.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from app.utils.indicators import (
    calculate_technical_indicators,
    add_rsi, add_macd, add_atr, add_bollinger_bands,
    add_stochastic, add_adx, add_williams_r, add_cci,
    add_roc, add_sma, add_ema, add_mfi, add_obv
)


class TestIndicators(unittest.TestCase):
    """Test cases for the indicators module."""

    def setUp(self):
        """Set up test fixtures."""
        # Create sample market data
        dates = [datetime.now() - timedelta(hours=i) for i in range(100)]
        self.sample_data = pd.DataFrame({
            'timestamp': [int(d.timestamp() * 1000) for d in dates],
            'date_timestamp': dates,
            'open': np.random.uniform(9000, 10000, 100),
            'high': np.random.uniform(10000, 11000, 100),
            'low': np.random.uniform(8000, 9000, 100),
            'close': np.random.uniform(9500, 10500, 100),
            'volume': np.random.uniform(1000, 2000, 100),
            'num_trades': np.random.randint(100, 1000, 100)
        })

    def test_calculate_technical_indicators(self):
        """Test that technical indicators are calculated correctly."""
        indicators = ['RSI', 'MACD', 'ATR', 'BB', 'STOCH', 'ADX', 'WillR', 'CCI', 'ROC', 'SMA', 'EMA', 'MFI', 'OBV']
        df_with_indicators = calculate_technical_indicators(self.sample_data, 'close', indicators)
        
        # Check that all indicators are added
        self.assertIn('RSI', df_with_indicators.columns)
        self.assertIn('MACD', df_with_indicators.columns)
        self.assertIn('ATR', df_with_indicators.columns)
        self.assertIn('BB_high', df_with_indicators.columns)
        self.assertIn('STOCH_k', df_with_indicators.columns)
        self.assertIn('ADX', df_with_indicators.columns)
        self.assertIn('WillR', df_with_indicators.columns)
        self.assertIn('CCI', df_with_indicators.columns)
        self.assertIn('ROC', df_with_indicators.columns)
        self.assertIn('SMA_5', df_with_indicators.columns)
        self.assertIn('EMA_5', df_with_indicators.columns)
        self.assertIn('MFI', df_with_indicators.columns)
        self.assertIn('OBV', df_with_indicators.columns)

    def test_add_rsi(self):
        """Test that RSI is calculated correctly."""
        df_with_rsi = add_rsi(self.sample_data, 'close')
        self.assertIn('RSI', df_with_rsi.columns)
        self.assertEqual(len(df_with_rsi), len(self.sample_data))
        
        # Check that RSI values are in the correct range
        rsi_values = df_with_rsi['RSI'].dropna()
        self.assertTrue(all(0 <= val <= 100 for val in rsi_values))

    def test_add_macd(self):
        """Test that MACD is calculated correctly."""
        df_with_macd = add_macd(self.sample_data, 'close')
        self.assertIn('MACD', df_with_macd.columns)
        self.assertIn('MACD_signal', df_with_macd.columns)
        self.assertIn('MACD_diff', df_with_macd.columns)
        self.assertEqual(len(df_with_macd), len(self.sample_data))

    def test_add_bollinger_bands(self):
        """Test that Bollinger Bands are calculated correctly."""
        df_with_bb = add_bollinger_bands(self.sample_data, 'close')
        self.assertIn('BB_high', df_with_bb.columns)
        self.assertIn('BB_low', df_with_bb.columns)
        self.assertIn('BB_mid', df_with_bb.columns)
        self.assertIn('BB_width', df_with_bb.columns)
        self.assertEqual(len(df_with_bb), len(self.sample_data))
        
        # Check that BB values are in the correct order
        bb_values = df_with_bb.dropna()
        self.assertTrue(all(bb_values['BB_high'] >= bb_values['BB_mid']))
        self.assertTrue(all(bb_values['BB_mid'] >= bb_values['BB_low']))


if __name__ == "__main__":
    unittest.main()
