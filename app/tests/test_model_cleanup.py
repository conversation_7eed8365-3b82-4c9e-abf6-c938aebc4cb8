#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for the model cleanup module.
"""

import unittest
from unittest.mock import patch, MagicMock
import os
import json
from datetime import datetime, timedelta

from app.utils.model_cleanup import (
    get_model_files,
    get_model_metadata,
    should_delete_model,
    cleanup_models,
    start_cleanup_service
)


class TestModelCleanup(unittest.TestCase):
    """Test cases for the model cleanup module."""

    def setUp(self):
        """Set up test fixtures."""
        # Create sample model files
        self.sample_model_files = [
            "BTCUSDT_model_20230101_120000.h5",
            "BTCUSDT_model_20230201_120000.h5",
            "BTCUSDT_model_20230301_120000.h5",
            "best_model.h5"
        ]
        
        # Create sample metadata
        self.sample_metadata = {
            "timestamp": "20230101_120000",
            "last_used": "20230101_120000",
            "usage_count": 10,
            "metrics": {"val_loss": 0.5}
        }
        
        # Create sample old metadata
        old_date = (datetime.now() - timedelta(days=100)).strftime("%Y%m%d_%H%M%S")
        self.sample_old_metadata = {
            "timestamp": old_date,
            "last_used": old_date,
            "usage_count": 1,
            "metrics": {"val_loss": 0.6}
        }

    @patch('app.utils.model_cleanup.os.listdir')
    def test_get_model_files(self, mock_listdir):
        """Test that model files are retrieved correctly."""
        # Mock the listdir function
        mock_listdir.return_value = self.sample_model_files + [
            "BTCUSDT_model_20230101_120000_metadata.json",
            "BTCUSDT_history_20230101_120000.json",
            "some_other_file.txt"
        ]
        
        # Get model files
        model_files = get_model_files("models/BTCUSDT")
        
        # Check that only .h5 files are returned
        self.assertEqual(len(model_files), 4)
        for file in model_files:
            self.assertTrue(file.endswith(".h5"))

    @patch('app.utils.model_cleanup.os.path.exists')
    @patch('app.utils.model_cleanup.json.load')
    @patch('app.utils.model_cleanup.open')
    def test_get_model_metadata(self, mock_open, mock_json_load, mock_exists):
        """Test that model metadata is retrieved correctly."""
        # Mock the dependencies
        mock_exists.return_value = True
        mock_json_load.return_value = self.sample_metadata
        mock_open.return_value = MagicMock()
        
        # Get model metadata
        metadata = get_model_metadata("models/BTCUSDT/BTCUSDT_model_20230101_120000.h5")
        
        # Check that the metadata is correct
        self.assertEqual(metadata, self.sample_metadata)

    def test_should_delete_model(self):
        """Test that model deletion decisions are made correctly."""
        # Test that best_model.h5 is never deleted
        self.assertFalse(should_delete_model(
            "models/BTCUSDT/best_model.h5",
            self.sample_metadata,
            max_age_days=30,
            max_versions=3
        ))
        
        # Test that old models are deleted
        self.assertTrue(should_delete_model(
            "models/BTCUSDT/BTCUSDT_model_20230101_120000.h5",
            self.sample_old_metadata,
            max_age_days=30,
            max_versions=3
        ))
        
        # Test that frequently used models are not deleted
        self.assertFalse(should_delete_model(
            "models/BTCUSDT/BTCUSDT_model_20230101_120000.h5",
            self.sample_metadata,
            max_age_days=30,
            max_versions=3
        ))

    @patch('app.utils.model_cleanup.get_model_files')
    @patch('app.utils.model_cleanup.get_model_metadata')
    @patch('app.utils.model_cleanup.should_delete_model')
    @patch('app.utils.model_cleanup.os.remove')
    @patch('app.utils.model_cleanup.os.path.exists')
    def test_cleanup_models(self, mock_exists, mock_remove, mock_should_delete, mock_get_metadata, mock_get_files):
        """Test that model cleanup works correctly."""
        # Mock the dependencies
        mock_get_files.return_value = self.sample_model_files
        mock_get_metadata.side_effect = [
            self.sample_metadata,
            self.sample_metadata,
            self.sample_old_metadata,
            self.sample_metadata
        ]
        mock_should_delete.side_effect = [False, False, True, False]
        mock_exists.return_value = True
        
        # Clean up models
        deleted_models = cleanup_models("models/BTCUSDT", max_age_days=30, max_versions=3)
        
        # Check that the correct number of models were deleted
        self.assertEqual(len(deleted_models), 1)
        self.assertEqual(deleted_models[0], "models/BTCUSDT/BTCUSDT_model_20230301_120000.h5")
        
        # Check that os.remove was called for the deleted model
        mock_remove.assert_called_once_with("models/BTCUSDT/BTCUSDT_model_20230301_120000.h5")

    @patch('app.utils.model_cleanup.threading.Timer')
    def test_start_cleanup_service(self, mock_timer):
        """Test that the cleanup service is started correctly."""
        # Mock the Timer class
        mock_timer_instance = MagicMock()
        mock_timer.return_value = mock_timer_instance
        
        # Start the cleanup service
        start_cleanup_service()
        
        # Check that the timer was created and started
        mock_timer.assert_called_once()
        mock_timer_instance.start.assert_called_once()


if __name__ == "__main__":
    unittest.main()
