#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for the trainer module.
"""

import unittest
from unittest.mock import patch, MagicMock
import os
import numpy as np
import tensorflow as tf
from datetime import datetime

from app.models.trainer import ModelTrainer


class TestTrainer(unittest.TestCase):
    """Test cases for the trainer module."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock model
        self.mock_model = MagicMock()
        self.mock_model.fit.return_value = MagicMock(history={
            'loss': [0.5, 0.4, 0.3],
            'val_loss': [0.6, 0.5, 0.4],
            'accuracy': [0.7, 0.8, 0.9],
            'val_accuracy': [0.6, 0.7, 0.8]
        })
        
        # Create sample data source
        self.sample_data_source = {
            "data_path": "data/BTCUSDT_1h.csv"
        }
        
        # Create sample training parameters
        self.sample_params = {
            "epochs": 10,
            "batch_size": 32,
            "learning_rate": 0.001
        }

    @patch('app.models.trainer.create_cnn_model')
    @patch('app.models.trainer.DataProcessor.prepare_training_data')
    @patch('app.models.trainer.pd.read_csv')
    @patch('app.utils.training_callbacks.TrainingProgressCallback')
    def test_train_with_data_path(self, mock_callback, mock_read_csv, mock_prepare_data, mock_create_model):
        """Test that training works correctly with a data path."""
        # Mock the dependencies
        mock_read_csv.return_value = MagicMock()
        mock_prepare_data.return_value = (
            np.random.random((100, 10, 10, 3)),  # x_train
            np.random.random((20, 10, 10, 3)),   # x_validation
            np.random.random((100, 2)),          # y_train
            np.random.random((20, 2)),           # y_validation
            np.array([0, 1])                     # labels
        )
        mock_create_model.return_value = self.mock_model
        mock_callback.return_value = MagicMock()
        
        # Initialize the trainer
        trainer = ModelTrainer(pair="BTCUSDT")
        
        # Train the model
        result = trainer.train(
            data_source=self.sample_data_source,
            params=self.sample_params,
            save_path="models/BTCUSDT",
            pair="BTCUSDT"
        )
        
        # Check that the model was created and trained
        mock_create_model.assert_called_once()
        self.mock_model.fit.assert_called_once()
        
        # Check that the result contains the expected keys
        self.assertIn("model_path", result)
        self.assertIn("metrics", result)
        self.assertIn("accuracy", result["metrics"])
        self.assertIn("f1_score", result["metrics"])

    @patch('app.models.trainer.create_cnn_model')
    @patch('app.models.trainer.DataProcessor.prepare_training_data')
    @patch('app.models.trainer.pd.DataFrame')
    @patch('app.utils.training_callbacks.TrainingProgressCallback')
    def test_train_with_market_data(self, mock_callback, mock_dataframe, mock_prepare_data, mock_create_model):
        """Test that training works correctly with market data."""
        # Mock the dependencies
        mock_dataframe.return_value = MagicMock()
        mock_prepare_data.return_value = (
            np.random.random((100, 10, 10, 3)),  # x_train
            np.random.random((20, 10, 10, 3)),   # x_validation
            np.random.random((100, 2)),          # y_train
            np.random.random((20, 2)),           # y_validation
            np.array([0, 1])                     # labels
        )
        mock_create_model.return_value = self.mock_model
        mock_callback.return_value = MagicMock()
        
        # Initialize the trainer
        trainer = ModelTrainer(pair="BTCUSDT")
        
        # Train the model with market data
        result = trainer.train(
            data_source={"market_data": [
                {
                    "timestamp": 1609459200000,
                    "open": 29000.0,
                    "high": 29300.0,
                    "low": 28800.0,
                    "close": 29100.0,
                    "volume": 1000.0,
                    "num_trades": 100,
                    "symbol": "BTCUSDT"
                }
            ]},
            params=self.sample_params,
            save_path="models/BTCUSDT",
            pair="BTCUSDT"
        )
        
        # Check that the model was created and trained
        mock_create_model.assert_called_once()
        self.mock_model.fit.assert_called_once()
        
        # Check that the result contains the expected keys
        self.assertIn("model_path", result)
        self.assertIn("metrics", result)

    @patch('app.models.trainer.os.path.exists')
    @patch('app.models.trainer.os.listdir')
    @patch('app.models.trainer.json.load')
    @patch('app.models.trainer.os.symlink')
    def test_compare_models(self, mock_symlink, mock_json_load, mock_listdir, mock_exists):
        """Test that model comparison works correctly."""
        # Mock the dependencies
        mock_exists.return_value = True
        mock_listdir.return_value = [
            "BTCUSDT_model_20230101_120000_metadata.json",
            "BTCUSDT_model_20230201_120000_metadata.json"
        ]
        mock_json_load.side_effect = [
            {
                "model_path": "models/BTCUSDT/BTCUSDT_model_20230101_120000.h5",
                "metrics": {"val_loss": 0.5}
            },
            {
                "model_path": "models/BTCUSDT/BTCUSDT_model_20230201_120000.h5",
                "metrics": {"val_loss": 0.4}
            }
        ]
        
        # Initialize the trainer
        trainer = ModelTrainer(pair="BTCUSDT")
        
        # Compare models
        current_model = {
            "model_path": "models/BTCUSDT/BTCUSDT_model_20230301_120000.h5",
            "metrics": {"val_loss": 0.3}
        }
        
        best_model = trainer.compare_models(
            models_dir="models/BTCUSDT",
            current_model=current_model,
            pair="BTCUSDT"
        )
        
        # Check that the best model is the current model (lowest val_loss)
        self.assertEqual(best_model["model_path"], current_model["model_path"])
        
        # Check that symlinks were created
        mock_symlink.assert_called()


if __name__ == "__main__":
    unittest.main()
