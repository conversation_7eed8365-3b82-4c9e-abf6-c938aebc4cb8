#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Mock TensorFlow module for testing without TensorFlow.
This module provides mock implementations of TensorFlow functions and classes.
"""

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class MockModel:
    """Mock implementation of a TensorFlow model."""
    
    def __init__(self, *args, **kwargs):
        logger.info("Initializing mock TensorFlow model")
        self.name = "mock_model"
    
    def predict(self, data, *args, **kwargs):
        """Mock prediction method."""
        logger.info(f"Mock prediction with data shape: {data.shape if hasattr(data, 'shape') else 'unknown'}")
        import numpy as np
        # Return random predictions (0 or 1)
        if hasattr(data, 'shape'):
            return np.random.randint(0, 2, size=(data.shape[0], 1))
        return np.array([[1]])
    
    def save(self, path, *args, **kwargs):
        """Mock save method."""
        logger.info(f"Mock saving model to {path}")
        # Create an empty file
        with open(path, "w") as f:
            f.write("Mock TensorFlow model")
    
    def load_weights(self, path, *args, **kwargs):
        """Mock load weights method."""
        logger.info(f"Mock loading weights from {path}")
        return self

class MockSequential:
    """Mock implementation of a TensorFlow Sequential model."""
    
    def __init__(self, *args, **kwargs):
        logger.info("Initializing mock TensorFlow Sequential model")
        self.name = "mock_sequential"
    
    def add(self, layer):
        """Mock add method."""
        logger.info(f"Mock adding layer: {layer}")
        return self
    
    def compile(self, *args, **kwargs):
        """Mock compile method."""
        logger.info("Mock compiling model")
        return self
    
    def fit(self, *args, **kwargs):
        """Mock fit method."""
        logger.info("Mock fitting model")
        return MockHistory()
    
    def predict(self, data, *args, **kwargs):
        """Mock prediction method."""
        logger.info(f"Mock prediction with data shape: {data.shape if hasattr(data, 'shape') else 'unknown'}")
        import numpy as np
        # Return random predictions (0 or 1)
        if hasattr(data, 'shape'):
            return np.random.randint(0, 2, size=(data.shape[0], 1))
        return np.array([[1]])
    
    def save(self, path, *args, **kwargs):
        """Mock save method."""
        logger.info(f"Mock saving model to {path}")
        # Create an empty file
        with open(path, "w") as f:
            f.write("Mock TensorFlow model")
    
    def load_weights(self, path, *args, **kwargs):
        """Mock load weights method."""
        logger.info(f"Mock loading weights from {path}")
        return self

class MockHistory:
    """Mock implementation of a TensorFlow History object."""
    
    def __init__(self, *args, **kwargs):
        import numpy as np
        self.history = {
            "loss": [0.5, 0.4, 0.3, 0.2, 0.1],
            "val_loss": [0.6, 0.5, 0.4, 0.3, 0.2],
            "accuracy": [0.5, 0.6, 0.7, 0.8, 0.9],
            "val_accuracy": [0.4, 0.5, 0.6, 0.7, 0.8]
        }

class MockLayer:
    """Mock implementation of a TensorFlow Layer."""
    
    def __init__(self, *args, **kwargs):
        self.name = "mock_layer"

class MockOptimizer:
    """Mock implementation of a TensorFlow Optimizer."""
    
    def __init__(self, *args, **kwargs):
        self.name = "mock_optimizer"

class MockLoss:
    """Mock implementation of a TensorFlow Loss."""
    
    def __init__(self, *args, **kwargs):
        self.name = "mock_loss"

class MockMetric:
    """Mock implementation of a TensorFlow Metric."""
    
    def __init__(self, *args, **kwargs):
        self.name = "mock_metric"

class MockCallback:
    """Mock implementation of a TensorFlow Callback."""
    
    def __init__(self, *args, **kwargs):
        self.name = "mock_callback"

class MockConfig:
    """Mock implementation of TensorFlow Config."""
    
    def __init__(self):
        pass
    
    def set_visible_devices(self, devices, device_type):
        logger.info(f"Mock setting visible devices: {devices}, type: {device_type}")

# Mock TensorFlow module
class MockTensorFlow:
    """Mock implementation of the TensorFlow module."""
    
    def __init__(self):
        self.keras = MockKeras()
        self.config = MockConfig()

# Mock Keras module
class MockKeras:
    """Mock implementation of the Keras module."""
    
    def __init__(self):
        self.models = MockModels()
        self.layers = MockLayers()
        self.optimizers = MockOptimizers()
        self.losses = MockLosses()
        self.metrics = MockMetrics()
        self.callbacks = MockCallbacks()
        self.Sequential = MockSequential

# Mock Models module
class MockModels:
    """Mock implementation of the Keras Models module."""
    
    def __init__(self):
        pass
    
    def Sequential(self, *args, **kwargs):
        return MockSequential(*args, **kwargs)
    
    def load_model(self, path, *args, **kwargs):
        logger.info(f"Mock loading model from {path}")
        return MockModel()

# Mock Layers module
class MockLayers:
    """Mock implementation of the Keras Layers module."""
    
    def __init__(self):
        pass
    
    def Dense(self, *args, **kwargs):
        return MockLayer()
    
    def Conv1D(self, *args, **kwargs):
        return MockLayer()
    
    def MaxPooling1D(self, *args, **kwargs):
        return MockLayer()
    
    def Flatten(self, *args, **kwargs):
        return MockLayer()
    
    def Dropout(self, *args, **kwargs):
        return MockLayer()
    
    def LSTM(self, *args, **kwargs):
        return MockLayer()
    
    def Input(self, *args, **kwargs):
        return MockLayer()

# Mock Optimizers module
class MockOptimizers:
    """Mock implementation of the Keras Optimizers module."""
    
    def __init__(self):
        pass
    
    def Adam(self, *args, **kwargs):
        return MockOptimizer()
    
    def SGD(self, *args, **kwargs):
        return MockOptimizer()
    
    def RMSprop(self, *args, **kwargs):
        return MockOptimizer()

# Mock Losses module
class MockLosses:
    """Mock implementation of the Keras Losses module."""
    
    def __init__(self):
        pass
    
    def binary_crossentropy(self, *args, **kwargs):
        return MockLoss()
    
    def categorical_crossentropy(self, *args, **kwargs):
        return MockLoss()
    
    def mean_squared_error(self, *args, **kwargs):
        return MockLoss()

# Mock Metrics module
class MockMetrics:
    """Mock implementation of the Keras Metrics module."""
    
    def __init__(self):
        pass
    
    def accuracy(self, *args, **kwargs):
        return MockMetric()
    
    def precision(self, *args, **kwargs):
        return MockMetric()
    
    def recall(self, *args, **kwargs):
        return MockMetric()

# Mock Callbacks module
class MockCallbacks:
    """Mock implementation of the Keras Callbacks module."""
    
    def __init__(self):
        pass
    
    def EarlyStopping(self, *args, **kwargs):
        return MockCallback()
    
    def ModelCheckpoint(self, *args, **kwargs):
        return MockCallback()
    
    def TensorBoard(self, *args, **kwargs):
        return MockCallback()

# Create mock TensorFlow instance
tf = MockTensorFlow()
