#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Training callbacks for the Trend Prediction Service.
This module provides custom callbacks for monitoring training progress.
"""

import logging
from typing import Dict, Any

import tensorflow as tf

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class TrainingProgressCallback(tf.keras.callbacks.Callback):
    """
    Callback for tracking training progress.
    """
    
    def __init__(self, training_status: Dict[str, Any]):
        """
        Initialize the callback.
        
        Args:
            training_status (Dict[str, Any]): Dictionary to update with training progress.
        """
        super().__init__()
        self.training_status = training_status
    
    def on_epoch_begin(self, epoch, logs=None):
        """Called at the start of an epoch."""
        self.training_status["current_epoch"] = epoch + 1
        self.training_status["progress"] = epoch / self.training_status["total_epochs"]
    
    def on_epoch_end(self, epoch, logs=None):
        """Called at the end of an epoch."""
        if logs:
            self.training_status["metrics"] = {
                "loss": float(logs.get("loss", 0)),
                "accuracy": float(logs.get("accuracy", 0)),
                "val_loss": float(logs.get("val_loss", 0)),
                "val_accuracy": float(logs.get("val_accuracy", 0))
            }
        
        self.training_status["progress"] = (epoch + 1) / self.training_status["total_epochs"]
    
    def on_train_begin(self, logs=None):
        """Called at the start of training."""
        self.training_status["is_training"] = True
        self.training_status["progress"] = 0.0
        self.training_status["error"] = None
    
    def on_train_end(self, logs=None):
        """Called at the end of training."""
        self.training_status["is_training"] = False
        self.training_status["progress"] = 1.0
        
        from datetime import datetime
        self.training_status["end_time"] = datetime.now().strftime("%Y%m%d_%H%M%S")
