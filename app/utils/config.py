#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration module for the Trend Prediction Service.
This module provides configuration settings for the application.
"""

import os

class Settings:
    """Application settings."""

    def __init__(self):
        # API settings
        self.API_TITLE = os.environ.get("API_TITLE", "Trend Prediction Service")
        self.API_DESCRIPTION = os.environ.get("API_DESCRIPTION", "API for predicting market trends using CNN models")
        self.API_VERSION = os.environ.get("API_VERSION", "1.0.0")

        # Model settings
        self.MODEL_DIR = os.environ.get("MODEL_DIR", "models")
        self.DEFAULT_PAIR = os.environ.get("DEFAULT_PAIR", "BTCUSDT")

        # Data settings
        self.DATA_DIR = os.environ.get("DATA_DIR", "data")

        # Logging settings
        self.LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")

        # Model cleanup settings
        self.MODEL_MAX_AGE_DAYS = int(os.environ.get("MODEL_MAX_AGE_DAYS", "90"))  # 3 months
        self.MODEL_MAX_VERSIONS = int(os.environ.get("MODEL_MAX_VERSIONS", "5"))  # Keep 5 versions per pair
        self.MODEL_CLEANUP_ENABLED = os.environ.get("MODEL_CLEANUP_ENABLED", "true").lower() == "true"

        # Training settings
        self.MAX_CONCURRENT_TRAININGS = int(os.environ.get("MAX_CONCURRENT_TRAININGS", "2"))

        # Supported pairs
        pairs_str = os.environ.get("SUPPORTED_PAIRS", "BTCUSDT")
        # Remove quotes if present
        pairs_str = pairs_str.replace('"', '')
        # Split by comma and strip whitespace
        self.SUPPORTED_PAIRS = [p.strip() for p in pairs_str.split(",")]

    def get_pair_model_dir(self, pair: str) -> str:
        """Get the model directory for a specific pair."""
        return os.path.join(self.MODEL_DIR, pair)

    def get_default_model_path(self, pair: str) -> str:
        """Get the default model path for a specific pair."""
        pair_dir = os.path.join(self.MODEL_DIR, pair)
        best_model_path = os.path.join(pair_dir, "best_model.h5")
        return os.environ.get("DEFAULT_MODEL_PATH", best_model_path)

    def is_pair_supported(self, pair: str) -> bool:
        """Check if a pair is supported."""
        return pair in self.SUPPORTED_PAIRS

# Create settings instance
settings = Settings()

# Ensure directories exist
os.makedirs(settings.MODEL_DIR, exist_ok=True)
os.makedirs(settings.DATA_DIR, exist_ok=True)

# Create directories for each supported pair
for pair in settings.SUPPORTED_PAIRS:
    pair_model_dir = settings.get_pair_model_dir(pair)
    os.makedirs(pair_model_dir, exist_ok=True)

    # Create symlink for best model if it doesn't exist
    best_model_link = os.path.join(pair_model_dir, "best_model.h5")
    if not os.path.exists(best_model_link):
        # Create an empty file as a placeholder
        with open(best_model_link + ".placeholder", "w") as f:
            f.write(f"Placeholder for {pair} best model")
