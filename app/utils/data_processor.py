#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Data processor module for the Trend Prediction Service.
This module provides functionality for processing market data for model training and prediction.
"""

import os
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Union, Any
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.model_selection import train_test_split

from app.utils.indicators import calculate_technical_indicators
from app.utils.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class DataProcessor:
    """
    Class for processing market data for model training and prediction.
    """

    def __init__(self):
        """Initialize the data processor."""
        self.scaler = StandardScaler()
        self.one_hot_encoder = OneHotEncoder(sparse_output=False, categories='auto')
        self.indicators = [
            'RSI', 'MACD', 'ATR', 'BB', 'STOCH', 'ADX', 'WillR', 'CCI', 'ROC',
            'SMA', 'EMA', 'MFI', 'OBV'
        ]

    def process_data(self, df: pd.DataFrame) -> np.ndarray:
        """
        Process market data for prediction.

        Args:
            df (pd.DataFrame): DataFrame containing market data.

        Returns:
            np.ndarray: Processed data ready for model input.

        Raises:
            ValueError: If the data processing fails.
        """
        try:
            # Calculate technical indicators
            df_with_indicators = self._calculate_indicators(df)

            # Drop unnecessary columns
            feature_df = df_with_indicators.drop(['timestamp', 'date_timestamp'], axis=1, errors='ignore')
            if 'close' in feature_df.columns:
                feature_df = feature_df.drop(['close'], axis=1)

            # Handle missing values
            feature_df = feature_df.dropna()

            # Scale features
            features = self.scaler.fit_transform(feature_df)

            # Transform to image format for CNN
            image_features = self._transform_to_image(features)

            return image_features

        except Exception as e:
            logger.error(f"Data processing error: {str(e)}")
            raise ValueError(f"Failed to process data: {str(e)}")

    def prepare_training_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        Prepare data for model training.

        Args:
            df (pd.DataFrame): DataFrame containing market data.

        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
                x_train, x_validation, y_train, y_validation, labels

        Raises:
            ValueError: If the data preparation fails.
        """
        try:
            # Calculate technical indicators
            df_with_indicators = self._calculate_indicators(df)

            # Create target variable (24h ahead price movement)
            window = 24  # Keep original 24-hour prediction window
            target_class = (df_with_indicators.shift(-window)['close'] > df_with_indicators['close'])
            df_with_indicators['target_class'] = target_class.astype(int)

            # Drop unnecessary columns and NaN values
            df_with_indicators = df_with_indicators.drop(['timestamp', 'date_timestamp', 'close'], axis=1, errors='ignore')
            df_with_indicators = df_with_indicators.dropna()

            # Check class balance
            class_counts = df_with_indicators['target_class'].value_counts()
            logger.info(f"Class distribution: {class_counts.to_dict()}")

            # Split features and target
            features = df_with_indicators.drop(['target_class'], axis=1)
            target = df_with_indicators['target_class']

            # Split data into training and validation sets (temporal split)
            split_idx = int(len(features) * 0.8)
            x_train = features.iloc[:split_idx]
            x_validation = features.iloc[split_idx:]
            y_train = target.iloc[:split_idx]
            y_validation = target.iloc[split_idx:]

            # Scale features (fit only on training data to prevent data leakage)
            x_train_scaled = self.scaler.fit_transform(x_train)
            x_validation_scaled = self.scaler.transform(x_validation)

            # Transform to image format for CNN
            x_train_img = self._transform_to_image(x_train_scaled)
            x_validation_img = self._transform_to_image(x_validation_scaled)

            # One-hot encode labels
            y_train_reshaped = y_train.values.reshape(-1, 1)
            y_validation_reshaped = y_validation.values.reshape(-1, 1)

            y_train_one_hot = self.one_hot_encoder.fit_transform(y_train_reshaped)
            y_validation_one_hot = self.one_hot_encoder.transform(y_validation_reshaped)

            # Get unique labels
            labels = np.unique(y_train)

            logger.info(f"Training data shape: {x_train_img.shape}")
            logger.info(f"Validation data shape: {x_validation_img.shape}")
            logger.info(f"Training labels shape: {y_train_one_hot.shape}")
            logger.info(f"Validation labels shape: {y_validation_one_hot.shape}")

            return x_train_img, x_validation_img, y_train_one_hot, y_validation_one_hot, labels

        except Exception as e:
            logger.error(f"Data preparation error: {str(e)}")
            raise ValueError(f"Failed to prepare training data: {str(e)}")

    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate technical indicators for the given market data.

        Args:
            df (pd.DataFrame): DataFrame containing market data.

        Returns:
            pd.DataFrame: DataFrame with added technical indicators.
        """
        # Make a copy to avoid modifying the original
        df_copy = df.copy()

        # Ensure required columns exist
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df_copy.columns:
                raise ValueError(f"Required column '{col}' not found in market data")

        # Calculate indicators
        df_with_indicators = calculate_technical_indicators(df_copy, 'close', self.indicators)

        return df_with_indicators

    def _transform_to_image(self, features: np.ndarray) -> np.ndarray:
        """
        Transform feature array to image format for CNN input.

        Args:
            features (np.ndarray): Feature array.

        Returns:
            np.ndarray: Image-like representation of features.
        """
        # Get number of features
        num_features = features.shape[1]

        # Calculate dimensions for a square image
        dim = int(np.ceil(np.sqrt(num_features)))

        # Pad features if necessary
        if dim * dim > num_features:
            padding = np.zeros((features.shape[0], dim * dim - num_features))
            features_padded = np.hstack((features, padding))
        else:
            features_padded = features

        # Reshape to square images
        images = features_padded.reshape(-1, dim, dim)

        # Stack to create 3-channel images (RGB)
        images_rgb = np.stack((images,) * 3, axis=-1)

        return images_rgb
