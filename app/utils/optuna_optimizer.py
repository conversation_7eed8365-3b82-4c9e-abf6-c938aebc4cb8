#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Advanced hyperparameter optimizer using Optuna for the Trend Prediction Service.
This module provides intelligent Bayesian optimization with automatic pruning.
"""

import logging
import numpy as np
import pandas as pd
import optuna
import json
import os
import gc
import tensorflow as tf
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
from sklearn.metrics import accuracy_score, f1_score

from app.utils.model_builder import create_cnn_model
from app.utils.data_processor import DataProcessor

logger = logging.getLogger(__name__)

# Suppress Optuna logs for cleaner output
optuna.logging.set_verbosity(optuna.logging.WARNING)

# Configure TensorFlow for memory efficiency
tf.config.experimental.enable_memory_growth = True


class OptunaHyperparameterOptimizer:
    """
    Advanced hyperparameter optimizer using Optuna for intelligent parameter search.

    Features:
    - Bayesian optimization with Tree-structured Parzen Estimator (TPE)
    - Automatic pruning of unpromising trials
    - Continuous and categorical parameter spaces
    - Memory of previous optimization runs
    - Integration with TensorFlow callbacks
    """

    def __init__(self, study_name: str = None, storage: str = None):
        """
        Initialize the Optuna optimizer.

        Args:
            study_name (str, optional): Name of the optimization study
            storage (str, optional): Database URL for persistent storage
        """
        self.data_processor = DataProcessor()
        self.study_name = study_name or f"cnn_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.storage = storage
        self.study = None
        self.best_params = None
        self.best_score = None

        # Training data cache
        self._x_train = None
        self._x_validation = None
        self._y_train = None
        self._y_validation = None
        self._labels = None

    def optimize(self, df: pd.DataFrame, n_trials: int = 20, metric: str = "f1_score",
                 timeout: Optional[int] = None, pruning: bool = True) -> Dict[str, Any]:
        """
        Optimize hyperparameters using Optuna.

        Args:
            df (pd.DataFrame): Training data
            n_trials (int): Number of optimization trials
            metric (str): Metric to optimize ("f1_score" or "accuracy")
            timeout (Optional[int]): Timeout in seconds
            pruning (bool): Enable automatic pruning of unpromising trials

        Returns:
            Dict[str, Any]: Optimization results
        """
        try:
            logger.info(f"Starting Optuna optimization with {n_trials} trials")

            # Prepare training data once
            self._prepare_training_data(df)

            # Create or load study
            direction = "maximize"  # We want to maximize F1 score or accuracy

            if pruning:
                pruner = optuna.pruners.MedianPruner(
                    n_startup_trials=5,
                    n_warmup_steps=10,
                    interval_steps=5
                )
            else:
                pruner = optuna.pruners.NopPruner()

            self.study = optuna.create_study(
                study_name=self.study_name,
                direction=direction,
                pruner=pruner,
                storage=self.storage,
                load_if_exists=True
            )

            # Define objective function
            def objective(trial):
                return self._objective(trial, metric)

            # Run optimization
            self.study.optimize(
                objective,
                n_trials=n_trials,
                timeout=timeout,
                show_progress_bar=True
            )

            # Get best results
            self.best_params = self.study.best_params
            self.best_score = self.study.best_value

            logger.info(f"Optimization completed. Best {metric}: {self.best_score:.4f}")
            logger.info(f"Best parameters: {self.best_params}")

            # Save results
            results = self._save_optimization_results()

            return results

        except Exception as e:
            logger.error(f"Optimization error: {str(e)}")
            raise ValueError(f"Failed to optimize hyperparameters: {str(e)}")

    def _prepare_training_data(self, df: pd.DataFrame):
        """Prepare training data for optimization."""
        logger.info("Preparing training data for optimization...")

        # Use the same data processor as training
        x_train, x_validation, y_train, y_validation, labels = self.data_processor.prepare_training_data(df)

        self._x_train = x_train
        self._x_validation = x_validation
        self._y_train = y_train
        self._y_validation = y_validation
        self._labels = labels

        logger.info(f"Training data prepared: {x_train.shape[0]} train, {x_validation.shape[0]} validation samples")

    def _objective(self, trial: optuna.Trial, metric: str) -> float:
        """
        Objective function for Optuna optimization with memory management.

        Args:
            trial (optuna.Trial): Optuna trial object
            metric (str): Metric to optimize

        Returns:
            float: Objective value to maximize
        """
        model = None
        try:
            # Suggest hyperparameters
            params = self._suggest_hyperparameters(trial)

            # Convert to model parameters format
            model_params = self._convert_to_model_params(params)

            # Create model
            model = create_cnn_model(
                params=model_params,
                x_train=self._x_train,
                labels=self._labels,
                multiclass=False
            )

            # Train with reduced epochs for optimization speed
            train_epochs = min(params["epochs"], 15)  # Limit for faster optimization

            # Add pruning callback for early stopping
            pruning_callback = optuna.integration.TFKerasPruningCallback(trial, "val_loss")

            # Add early stopping for efficiency
            early_stopping = tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=5,
                restore_best_weights=True,
                verbose=0
            )

            # Train model
            history = model.fit(
                self._x_train, self._y_train,
                validation_data=(self._x_validation, self._y_validation),
                epochs=train_epochs,
                batch_size=params["batch_size"],
                callbacks=[pruning_callback, early_stopping],
                verbose=0
            )

            # Evaluate model on validation set
            y_pred = model.predict(self._x_validation, verbose=0)
            y_pred_classes = np.argmax(y_pred, axis=1)
            y_true_classes = np.argmax(self._y_validation, axis=1)

            # Calculate metrics
            accuracy = accuracy_score(y_true_classes, y_pred_classes)
            f1 = f1_score(y_true_classes, y_pred_classes, average='weighted', zero_division=0)

            # Get validation loss
            val_loss = min(history.history.get('val_loss', [1.0]))

            # Return the metric to optimize
            score = f1 if metric == "f1_score" else accuracy

            # Store trial results
            trial.set_user_attr("accuracy", float(accuracy))
            trial.set_user_attr("f1_score", float(f1))
            trial.set_user_attr("val_loss", float(val_loss))
            trial.set_user_attr("epochs_trained", len(history.history['loss']))

            logger.debug(f"Trial {trial.number}: {metric}={score:.4f}, accuracy={accuracy:.4f}, f1={f1:.4f}, val_loss={val_loss:.4f}")

            return float(score)

        except optuna.TrialPruned:
            # Trial was pruned - this is expected behavior
            logger.debug(f"Trial {trial.number} was pruned")
            raise
        except Exception as e:
            logger.warning(f"Trial {trial.number} failed: {str(e)}")
            return 0.0  # Return worst possible score for failed trials
        finally:
            # Clean up memory
            if model is not None:
                del model
            tf.keras.backend.clear_session()
            gc.collect()

    def _suggest_hyperparameters(self, trial: optuna.Trial) -> Dict[str, Any]:
        """
        Suggest hyperparameters for the trial with extensively expanded search space.

        Args:
            trial (optuna.Trial): Optuna trial object

        Returns:
            Dict[str, Any]: Suggested hyperparameters
        """
        return {
            # Training parameters - extensively expanded ranges
            "epochs": trial.suggest_int("epochs", 32, 128, step=16),
            "batch_size": trial.suggest_categorical("batch_size", [24, 32, 40, 48, 60, 80, 100, 120, 140]),
            "learning_rate": trial.suggest_float("learning_rate", 0.0001, 0.02, log=True),

            # CNN architecture - extensively more options
            "conv_filters_1": trial.suggest_int("conv_filters_1", 8, 48, step=4),
            "conv_filters_2": trial.suggest_int("conv_filters_2", 16, 96, step=8),
            "conv_filters_3": trial.suggest_int("conv_filters_3", 32, 128, step=16),  # Third conv layer
            "dense_nodes_1": trial.suggest_int("dense_nodes_1", 12, 96, step=8),
            "dense_nodes_2": trial.suggest_int("dense_nodes_2", 8, 64, step=8),  # Second dense layer

            # Regularization - extensively fine-tuned ranges
            "dropout_conv_1": trial.suggest_float("dropout_conv_1", 0.02, 0.4),
            "dropout_conv_2": trial.suggest_float("dropout_conv_2", 0.01, 0.3),
            "dropout_conv_3": trial.suggest_float("dropout_conv_3", 0.05, 0.35),  # Third conv dropout
            "dropout_dense_1": trial.suggest_float("dropout_dense_1", 0.1, 0.6),
            "dropout_dense_2": trial.suggest_float("dropout_dense_2", 0.05, 0.4),  # Second dense dropout

            # Additional optimization parameters - more options
            "kernel_size_1": trial.suggest_categorical("kernel_size_1", [2, 3, 4]),
            "kernel_size_2": trial.suggest_categorical("kernel_size_2", [2, 3, 4]),
            "kernel_size_3": trial.suggest_categorical("kernel_size_3", [2, 3]),  # Third conv kernel
            "activation": trial.suggest_categorical("activation", ["relu", "elu", "swish", "gelu", "tanh"]),
            "optimizer_type": trial.suggest_categorical("optimizer_type", ["adam", "adamw", "rmsprop"]),
            "beta_1": trial.suggest_float("beta_1", 0.85, 0.95),  # Adam beta_1
            "beta_2": trial.suggest_float("beta_2", 0.99, 0.999),  # Adam beta_2
            "weight_decay": trial.suggest_float("weight_decay", 1e-6, 1e-3, log=True),  # L2 regularization

            # Advanced architecture options
            "use_batch_norm": trial.suggest_categorical("use_batch_norm", [True, False]),
            "pool_size_1": trial.suggest_categorical("pool_size_1", [2, 3]),
            "pool_size_2": trial.suggest_categorical("pool_size_2", [2, 3]),
            "strides_1": trial.suggest_categorical("strides_1", [1, 2]),
            "strides_2": trial.suggest_categorical("strides_2", [1, 2]),
        }

    def _convert_to_model_params(self, opt_params: Dict) -> Dict:
        """
        Convert optimization parameters to model parameters format with extensive support.

        Args:
            opt_params (Dict): Optimization parameters

        Returns:
            Dict: Model parameters
        """
        return {
            'batch_size': opt_params['batch_size'],
            'epochs': opt_params['epochs'],
            'lr': opt_params['learning_rate'],
            'optimizer': opt_params.get('optimizer_type', 'adam'),
            'activation': opt_params.get('activation', 'relu'),
            'beta_1': opt_params.get('beta_1', 0.9),
            'beta_2': opt_params.get('beta_2', 0.999),
            'weight_decay': opt_params.get('weight_decay', 1e-4),
            'use_batch_norm': opt_params.get('use_batch_norm', False),
            'conv2d_layers': {
                # First conv layer
                'conv2d_drop_out_1': opt_params['dropout_conv_1'],
                'filters_1': opt_params['conv_filters_1'],
                'kernel_size_1': opt_params.get('kernel_size_1', 2),
                'conv2d_mp_1': opt_params.get('pool_size_1', 2),
                'strides_1': opt_params.get('strides_1', 1),
                'kernel_regularizer_1': opt_params.get('weight_decay', 1e-4),

                # Second conv layer
                'conv2_drop_out_2': opt_params['dropout_conv_2'],
                'filters_2': opt_params['conv_filters_2'],
                'kernel_size_2': opt_params.get('kernel_size_2', 2),
                'conv2d_mp_2': opt_params.get('pool_size_2', 2),
                'strides_2': opt_params.get('strides_2', 1),
                'kernel_regularizer_2': opt_params.get('weight_decay', 1e-4),

                # Third conv layer (optional)
                'conv2_drop_out_3': opt_params.get('dropout_conv_3', 0.2),
                'filters_3': opt_params.get('conv_filters_3', 64),
                'kernel_size_3': opt_params.get('kernel_size_3', 2),
                'conv2d_mp_3': 2,
                'strides_3': 1,
                'kernel_regularizer_3': opt_params.get('weight_decay', 1e-4),
                'use_third_conv': True  # Enable third conv layer
            },
            'dense_layers': {
                # First dense layer
                'dense_drop_out_1': opt_params.get('dropout_dense_1', opt_params.get('dropout_dense', 0.3)),
                'dense_nodes_1': opt_params.get('dense_nodes_1', opt_params.get('dense_nodes', 32)),
                'kernel_regularizer_1': opt_params.get('weight_decay', 1e-4),

                # Second dense layer (optional)
                'dense_drop_out_2': opt_params.get('dropout_dense_2', 0.2),
                'dense_nodes_2': opt_params.get('dense_nodes_2', 16),
                'kernel_regularizer_2': opt_params.get('weight_decay', 1e-4),
                'use_second_dense': True  # Enable second dense layer
            }
        }

    def _save_optimization_results(self) -> Dict[str, Any]:
        """
        Save optimization results to file with proper directory structure.

        Returns:
            Dict[str, Any]: Optimization results
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create optimization results directory
        results_dir = "models/optimization_results"
        os.makedirs(results_dir, exist_ok=True)

        # Prepare comprehensive results
        results = {
            "timestamp": timestamp,
            "study_name": self.study_name,
            "best_params": self.best_params,
            "best_score": self.best_score,
            "n_trials": len(self.study.trials),
            "study_statistics": {
                "completed_trials": len([t for t in self.study.trials if t.state == optuna.trial.TrialState.COMPLETE]),
                "pruned_trials": len([t for t in self.study.trials if t.state == optuna.trial.TrialState.PRUNED]),
                "failed_trials": len([t for t in self.study.trials if t.state == optuna.trial.TrialState.FAIL]),
            },
            "optimization_history": []
        }

        # Add trial history with more details
        for trial in self.study.trials:
            trial_data = {
                "trial_number": trial.number,
                "params": trial.params,
                "value": trial.value,
                "state": trial.state.name,
                "user_attrs": trial.user_attrs,
                "datetime_start": trial.datetime_start.isoformat() if trial.datetime_start else None,
                "datetime_complete": trial.datetime_complete.isoformat() if trial.datetime_complete else None,
                "duration": (trial.datetime_complete - trial.datetime_start).total_seconds() if trial.datetime_complete and trial.datetime_start else None
            }
            results["optimization_history"].append(trial_data)

        # Save to file
        filename = f"optuna_optimization_{timestamp}.json"
        filepath = os.path.join(results_dir, filename)

        try:
            with open(filepath, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            logger.info(f"Optimization results saved to {filepath}")

            # Also save a summary file with just the best results
            summary_filename = f"optuna_summary_{timestamp}.json"
            summary_filepath = os.path.join(results_dir, summary_filename)

            summary = {
                "timestamp": timestamp,
                "study_name": self.study_name,
                "best_params": self.best_params,
                "best_score": self.best_score,
                "n_trials": len(self.study.trials),
                "study_statistics": results["study_statistics"]
            }

            with open(summary_filepath, 'w') as f:
                json.dump(summary, f, indent=2, default=str)

            logger.info(f"Optimization summary saved to {summary_filepath}")

        except Exception as e:
            logger.error(f"Failed to save optimization results: {str(e)}")
            # Return results even if saving failed

        return results

    def get_best_params(self) -> Dict[str, Any]:
        """
        Get the best parameters found during optimization.

        Returns:
            Dict[str, Any]: Best parameters
        """
        if self.best_params is None:
            # Return research-validated default parameters
            return {
                "epochs": 64,
                "batch_size": 80,
                "learning_rate": 0.001,
                "conv_filters_1": 20,
                "conv_filters_2": 40,
                "dense_nodes": 32,
                "dropout_conv_1": 0.22,
                "dropout_conv_2": 0.05,
                "dropout_dense": 0.22
            }

        return self.best_params.copy()

    def convert_to_model_params(self, opt_params: Dict = None) -> Dict:
        """
        Public method to convert optimization parameters to model parameters format.

        Args:
            opt_params (Dict, optional): Optimization parameters. If None, uses best_params.

        Returns:
            Dict: Model parameters
        """
        if opt_params is None:
            opt_params = self.get_best_params()

        return self._convert_to_model_params(opt_params)

    def get_optimization_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the optimization process.

        Returns:
            Dict[str, Any]: Optimization summary
        """
        if self.study is None:
            return {"status": "No optimization performed"}

        return {
            "study_name": self.study_name,
            "n_trials": len(self.study.trials),
            "best_score": self.best_score,
            "best_params": self.best_params,
            "pruned_trials": len([t for t in self.study.trials if t.state == optuna.trial.TrialState.PRUNED]),
            "failed_trials": len([t for t in self.study.trials if t.state == optuna.trial.TrialState.FAIL]),
            "completed_trials": len([t for t in self.study.trials if t.state == optuna.trial.TrialState.COMPLETE])
        }
