#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Advanced hyperparameter optimizer using Optuna for the Trend Prediction Service.
This module provides intelligent Bayesian optimization with automatic pruning.
"""

import logging
import numpy as np
import pandas as pd
import optuna
import json
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
from sklearn.metrics import accuracy_score, f1_score

from app.utils.model_builder import create_cnn_model
from app.utils.data_processor import DataProcessor

logger = logging.getLogger(__name__)

# Suppress Optuna logs for cleaner output
optuna.logging.set_verbosity(optuna.logging.WARNING)


class OptunaHyperparameterOptimizer:
    """
    Advanced hyperparameter optimizer using Optuna for intelligent parameter search.
    
    Features:
    - Bayesian optimization with Tree-structured Parzen Estimator (TPE)
    - Automatic pruning of unpromising trials
    - Continuous and categorical parameter spaces
    - Memory of previous optimization runs
    - Integration with TensorFlow callbacks
    """

    def __init__(self, study_name: str = None, storage: str = None):
        """
        Initialize the Optuna optimizer.

        Args:
            study_name (str, optional): Name of the optimization study
            storage (str, optional): Database URL for persistent storage
        """
        self.data_processor = DataProcessor()
        self.study_name = study_name or f"cnn_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.storage = storage
        self.study = None
        self.best_params = None
        self.best_score = None
        
        # Training data cache
        self._x_train = None
        self._x_validation = None
        self._y_train = None
        self._y_validation = None
        self._labels = None

    def optimize(self, df: pd.DataFrame, n_trials: int = 20, metric: str = "f1_score", 
                 timeout: Optional[int] = None, pruning: bool = True) -> Dict[str, Any]:
        """
        Optimize hyperparameters using Optuna.

        Args:
            df (pd.DataFrame): Training data
            n_trials (int): Number of optimization trials
            metric (str): Metric to optimize ("f1_score" or "accuracy")
            timeout (Optional[int]): Timeout in seconds
            pruning (bool): Enable automatic pruning of unpromising trials

        Returns:
            Dict[str, Any]: Optimization results
        """
        try:
            logger.info(f"Starting Optuna optimization with {n_trials} trials")
            
            # Prepare training data once
            self._prepare_training_data(df)
            
            # Create or load study
            direction = "maximize"  # We want to maximize F1 score or accuracy
            
            if pruning:
                pruner = optuna.pruners.MedianPruner(
                    n_startup_trials=5,
                    n_warmup_steps=10,
                    interval_steps=5
                )
            else:
                pruner = optuna.pruners.NopPruner()
            
            self.study = optuna.create_study(
                study_name=self.study_name,
                direction=direction,
                pruner=pruner,
                storage=self.storage,
                load_if_exists=True
            )
            
            # Define objective function
            def objective(trial):
                return self._objective(trial, metric)
            
            # Run optimization
            self.study.optimize(
                objective,
                n_trials=n_trials,
                timeout=timeout,
                show_progress_bar=True
            )
            
            # Get best results
            self.best_params = self.study.best_params
            self.best_score = self.study.best_value
            
            logger.info(f"Optimization completed. Best {metric}: {self.best_score:.4f}")
            logger.info(f"Best parameters: {self.best_params}")
            
            # Save results
            results = self._save_optimization_results()
            
            return results
            
        except Exception as e:
            logger.error(f"Optimization error: {str(e)}")
            raise ValueError(f"Failed to optimize hyperparameters: {str(e)}")

    def _prepare_training_data(self, df: pd.DataFrame):
        """Prepare training data for optimization."""
        logger.info("Preparing training data for optimization...")
        
        # Use the same data processor as training
        x_train, x_validation, y_train, y_validation, labels = self.data_processor.prepare_training_data(df)
        
        self._x_train = x_train
        self._x_validation = x_validation
        self._y_train = y_train
        self._y_validation = y_validation
        self._labels = labels
        
        logger.info(f"Training data prepared: {x_train.shape[0]} train, {x_validation.shape[0]} validation samples")

    def _objective(self, trial: optuna.Trial, metric: str) -> float:
        """
        Objective function for Optuna optimization.

        Args:
            trial (optuna.Trial): Optuna trial object
            metric (str): Metric to optimize

        Returns:
            float: Objective value to maximize
        """
        try:
            # Suggest hyperparameters
            params = self._suggest_hyperparameters(trial)
            
            # Convert to model parameters format
            model_params = self._convert_to_model_params(params)
            
            # Create model
            model = create_cnn_model(
                params=model_params,
                x_train=self._x_train,
                labels=self._labels,
                multiclass=False
            )
            
            # Train with reduced epochs for optimization speed
            train_epochs = min(params["epochs"], 15)  # Limit for faster optimization
            
            # Add pruning callback
            pruning_callback = optuna.integration.TFKerasPruningCallback(trial, "val_loss")
            
            history = model.fit(
                self._x_train, self._y_train,
                validation_data=(self._x_validation, self._y_validation),
                epochs=train_epochs,
                batch_size=params["batch_size"],
                callbacks=[pruning_callback],
                verbose=0
            )
            
            # Evaluate model
            y_pred = model.predict(self._x_validation, verbose=0)
            y_pred_classes = np.argmax(y_pred, axis=1)
            y_true_classes = np.argmax(self._y_validation, axis=1)
            
            accuracy = accuracy_score(y_true_classes, y_pred_classes)
            f1 = f1_score(y_true_classes, y_pred_classes)
            
            # Return the metric to optimize
            score = f1 if metric == "f1_score" else accuracy
            
            # Log trial results
            trial.set_user_attr("accuracy", accuracy)
            trial.set_user_attr("f1_score", f1)
            trial.set_user_attr("val_loss", min(history.history['val_loss']))
            
            logger.debug(f"Trial {trial.number}: {metric}={score:.4f}, accuracy={accuracy:.4f}, f1={f1:.4f}")
            
            return score
            
        except optuna.TrialPruned:
            # Trial was pruned
            logger.debug(f"Trial {trial.number} was pruned")
            raise
        except Exception as e:
            logger.warning(f"Trial {trial.number} failed: {str(e)}")
            return 0.0  # Return worst possible score for failed trials

    def _suggest_hyperparameters(self, trial: optuna.Trial) -> Dict[str, Any]:
        """
        Suggest hyperparameters for the trial.

        Args:
            trial (optuna.Trial): Optuna trial object

        Returns:
            Dict[str, Any]: Suggested hyperparameters
        """
        return {
            # Training parameters
            "epochs": trial.suggest_int("epochs", 32, 80, step=16),
            "batch_size": trial.suggest_categorical("batch_size", [40, 60, 80, 100]),
            "learning_rate": trial.suggest_float("learning_rate", 0.0005, 0.005, log=True),
            
            # CNN architecture
            "conv_filters_1": trial.suggest_int("conv_filters_1", 16, 32, step=4),
            "conv_filters_2": trial.suggest_int("conv_filters_2", 32, 64, step=8),
            "dense_nodes": trial.suggest_int("dense_nodes", 24, 64, step=8),
            
            # Regularization
            "dropout_conv_1": trial.suggest_float("dropout_conv_1", 0.1, 0.3),
            "dropout_conv_2": trial.suggest_float("dropout_conv_2", 0.05, 0.2),
            "dropout_dense": trial.suggest_float("dropout_dense", 0.15, 0.4),
        }

    def _convert_to_model_params(self, opt_params: Dict) -> Dict:
        """
        Convert optimization parameters to model parameters format.

        Args:
            opt_params (Dict): Optimization parameters

        Returns:
            Dict: Model parameters
        """
        return {
            'batch_size': opt_params['batch_size'],
            'epochs': opt_params['epochs'],
            'lr': opt_params['learning_rate'],
            'optimizer': 'adam',
            'conv2d_layers': {
                'conv2d_drop_out_1': opt_params['dropout_conv_1'],
                'filters_1': opt_params['conv_filters_1'],
                'kernel_size_1': 2,
                'conv2d_mp_1': 2,
                'strides_1': 1,
                'kernel_regularizer_1': 0.0,
                'conv2_drop_out_2': opt_params['dropout_conv_2'],
                'filters_2': opt_params['conv_filters_2'],
                'kernel_size_2': 2,
                'conv2d_mp_2': 2,
                'strides_2': 1,
                'kernel_regularizer_2': 0.0
            },
            'dense_layers': {
                'dense_drop_out_1': opt_params['dropout_dense'],
                'dense_nodes_1': opt_params['dense_nodes'],
                'kernel_regularizer_1': 0.0
            }
        }

    def _save_optimization_results(self) -> Dict[str, Any]:
        """
        Save optimization results to file.

        Returns:
            Dict[str, Any]: Optimization results
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Prepare results
        results = {
            "timestamp": timestamp,
            "study_name": self.study_name,
            "best_params": self.best_params,
            "best_score": self.best_score,
            "n_trials": len(self.study.trials),
            "optimization_history": []
        }
        
        # Add trial history
        for trial in self.study.trials:
            trial_data = {
                "trial_number": trial.number,
                "params": trial.params,
                "value": trial.value,
                "state": trial.state.name,
                "user_attrs": trial.user_attrs
            }
            results["optimization_history"].append(trial_data)
        
        # Save to file
        filename = f"optuna_optimization_{timestamp}.json"
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Optimization results saved to {filename}")
        
        return results

    def get_best_params(self) -> Dict[str, Any]:
        """
        Get the best parameters found during optimization.

        Returns:
            Dict[str, Any]: Best parameters
        """
        if self.best_params is None:
            # Return research-validated default parameters
            return {
                "epochs": 64,
                "batch_size": 80,
                "learning_rate": 0.001,
                "conv_filters_1": 20,
                "conv_filters_2": 40,
                "dense_nodes": 32,
                "dropout_conv_1": 0.22,
                "dropout_conv_2": 0.05,
                "dropout_dense": 0.22
            }
        
        return self.best_params.copy()

    def get_optimization_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the optimization process.

        Returns:
            Dict[str, Any]: Optimization summary
        """
        if self.study is None:
            return {"status": "No optimization performed"}
        
        return {
            "study_name": self.study_name,
            "n_trials": len(self.study.trials),
            "best_score": self.best_score,
            "best_params": self.best_params,
            "pruned_trials": len([t for t in self.study.trials if t.state == optuna.trial.TrialState.PRUNED]),
            "failed_trials": len([t for t in self.study.trials if t.state == optuna.trial.TrialState.FAIL]),
            "completed_trials": len([t for t in self.study.trials if t.state == optuna.trial.TrialState.COMPLETE])
        }
