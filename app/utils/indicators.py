#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Technical indicators module for the Trend Prediction Service.
This module provides functionality for calculating technical indicators.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union

import ta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

def calculate_technical_indicators(df: pd.DataFrame, price_column: str, indicators: List[str]) -> pd.DataFrame:
    """
    Calculate technical indicators for the given market data.
    
    Args:
        df (pd.DataFrame): DataFrame containing market data.
        price_column (str): Column name for price data.
        indicators (List[str]): List of indicator names to calculate.
    
    Returns:
        pd.DataFrame: DataFrame with added technical indicators.
    """
    # Make a copy to avoid modifying the original
    df_copy = df.copy()
    
    # Calculate indicators
    for indicator in indicators:
        try:
            if indicator == 'RSI':
                df_copy = add_rsi(df_copy, price_column)
            elif indicator == 'MACD':
                df_copy = add_macd(df_copy, price_column)
            elif indicator == 'ATR':
                df_copy = add_atr(df_copy)
            elif indicator == 'BB':
                df_copy = add_bollinger_bands(df_copy, price_column)
            elif indicator == 'STOCH':
                df_copy = add_stochastic(df_copy)
            elif indicator == 'ADX':
                df_copy = add_adx(df_copy)
            elif indicator == 'WillR':
                df_copy = add_williams_r(df_copy)
            elif indicator == 'CCI':
                df_copy = add_cci(df_copy)
            elif indicator == 'ROC':
                df_copy = add_roc(df_copy, price_column)
            elif indicator == 'SMA':
                df_copy = add_sma(df_copy, price_column)
            elif indicator == 'EMA':
                df_copy = add_ema(df_copy, price_column)
            elif indicator == 'MFI':
                df_copy = add_mfi(df_copy)
            elif indicator == 'OBV':
                df_copy = add_obv(df_copy)
            else:
                logger.warning(f"Unknown indicator: {indicator}")
        except Exception as e:
            logger.error(f"Error calculating {indicator}: {str(e)}")
    
    return df_copy

def add_rsi(df: pd.DataFrame, price_column: str, window: int = 14) -> pd.DataFrame:
    """Add Relative Strength Index (RSI) to the DataFrame."""
    df['RSI'] = ta.momentum.RSIIndicator(df[price_column], window=window).rsi()
    return df

def add_macd(df: pd.DataFrame, price_column: str, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.DataFrame:
    """Add Moving Average Convergence Divergence (MACD) to the DataFrame."""
    macd = ta.trend.MACD(df[price_column], window_fast=fast, window_slow=slow, window_sign=signal)
    df['MACD'] = macd.macd()
    df['MACD_signal'] = macd.macd_signal()
    df['MACD_diff'] = macd.macd_diff()
    return df

def add_atr(df: pd.DataFrame, window: int = 14) -> pd.DataFrame:
    """Add Average True Range (ATR) to the DataFrame."""
    df['ATR'] = ta.volatility.AverageTrueRange(high=df['high'], low=df['low'], close=df['close'], window=window).average_true_range()
    return df

def add_bollinger_bands(df: pd.DataFrame, price_column: str, window: int = 20, window_dev: int = 2) -> pd.DataFrame:
    """Add Bollinger Bands to the DataFrame."""
    bollinger = ta.volatility.BollingerBands(df[price_column], window=window, window_dev=window_dev)
    df['BB_high'] = bollinger.bollinger_hband()
    df['BB_low'] = bollinger.bollinger_lband()
    df['BB_mid'] = bollinger.bollinger_mavg()
    df['BB_width'] = bollinger.bollinger_wband()
    return df

def add_stochastic(df: pd.DataFrame, window: int = 14, smooth_window: int = 3) -> pd.DataFrame:
    """Add Stochastic Oscillator to the DataFrame."""
    stoch = ta.momentum.StochasticOscillator(high=df['high'], low=df['low'], close=df['close'], window=window, smooth_window=smooth_window)
    df['STOCH_k'] = stoch.stoch()
    df['STOCH_d'] = stoch.stoch_signal()
    return df

def add_adx(df: pd.DataFrame, window: int = 14) -> pd.DataFrame:
    """Add Average Directional Index (ADX) to the DataFrame."""
    adx = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close'], window=window)
    df['ADX'] = adx.adx()
    df['ADX_pos'] = adx.adx_pos()
    df['ADX_neg'] = adx.adx_neg()
    return df

def add_williams_r(df: pd.DataFrame, window: int = 14) -> pd.DataFrame:
    """Add Williams %R to the DataFrame."""
    df['WillR'] = ta.momentum.WilliamsRIndicator(high=df['high'], low=df['low'], close=df['close'], lbp=window).williams_r()
    return df

def add_cci(df: pd.DataFrame, window: int = 20) -> pd.DataFrame:
    """Add Commodity Channel Index (CCI) to the DataFrame."""
    df['CCI'] = ta.trend.CCIIndicator(high=df['high'], low=df['low'], close=df['close'], window=window).cci()
    return df

def add_roc(df: pd.DataFrame, price_column: str, window: int = 12) -> pd.DataFrame:
    """Add Rate of Change (ROC) to the DataFrame."""
    df['ROC'] = ta.momentum.ROCIndicator(df[price_column], window=window).roc()
    return df

def add_sma(df: pd.DataFrame, price_column: str, windows: List[int] = [5, 10, 20, 50, 200]) -> pd.DataFrame:
    """Add Simple Moving Averages (SMA) to the DataFrame."""
    for window in windows:
        df[f'SMA_{window}'] = ta.trend.SMAIndicator(df[price_column], window=window).sma_indicator()
    return df

def add_ema(df: pd.DataFrame, price_column: str, windows: List[int] = [5, 10, 20, 50, 200]) -> pd.DataFrame:
    """Add Exponential Moving Averages (EMA) to the DataFrame."""
    for window in windows:
        df[f'EMA_{window}'] = ta.trend.EMAIndicator(df[price_column], window=window).ema_indicator()
    return df

def add_mfi(df: pd.DataFrame, window: int = 14) -> pd.DataFrame:
    """Add Money Flow Index (MFI) to the DataFrame."""
    df['MFI'] = ta.volume.MFIIndicator(high=df['high'], low=df['low'], close=df['close'], volume=df['volume'], window=window).money_flow_index()
    return df

def add_obv(df: pd.DataFrame) -> pd.DataFrame:
    """Add On-Balance Volume (OBV) to the DataFrame."""
    df['OBV'] = ta.volume.OnBalanceVolumeIndicator(close=df['close'], volume=df['volume']).on_balance_volume()
    return df
