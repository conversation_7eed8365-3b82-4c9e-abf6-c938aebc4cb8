#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Model builder module for the Trend Prediction Service.
This module provides functionality for creating CNN models.
"""

import logging
import numpy as np
from typing import Dict, List, Any

import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, Dropout, Flatten, Dense
from tensorflow.keras import regularizers, optimizers

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

def create_cnn_model(params: Dict[str, Any], x_train: np.ndarray, labels: np.ndarray, multiclass: bool = False) -> tf.keras.Model:
    """
    Create and compile a CNN model based on the provided parameters.

    Args:
        params (Dict[str, Any]): Dictionary containing model parameters.
        x_train (np.ndarray): Training data in image format.
        labels (np.ndarray): Array of unique labels.
        multiclass (bool): Whether the problem is multi-class or binary.

    Returns:
        tf.keras.Model: Compiled CNN model.
    """
    try:
        model = Sequential()
        
        # Add the first Conv2D layer
        model.add(Conv2D(
            filters=params['conv2d_layers']['filters_1'],
            kernel_size=params['conv2d_layers']['kernel_size_1'],
            strides=params['conv2d_layers']['strides_1'],
            kernel_regularizer=regularizers.l2(params['conv2d_layers']['kernel_regularizer_1']),
            padding='valid',
            activation='relu',
            use_bias=True,
            kernel_initializer='glorot_uniform',
            input_shape=(x_train.shape[1], x_train.shape[2], x_train.shape[3])
        ))
        model.add(Dropout(params['conv2d_layers']['conv2d_drop_out_1']))
        
        # Add the second Conv2D layer if specified
        if 'filters_2' in params['conv2d_layers']:
            model.add(Conv2D(
                filters=params['conv2d_layers']['filters_2'],
                kernel_size=params['conv2d_layers']['kernel_size_2'],
                strides=params['conv2d_layers']['strides_2'],
                kernel_regularizer=regularizers.l2(params['conv2d_layers']['kernel_regularizer_2']),
                padding='valid',
                activation='relu',
                use_bias=True,
                kernel_initializer='glorot_uniform'
            ))
            model.add(Dropout(params['conv2d_layers']['conv2_drop_out_2']))
        
        # Flatten layer
        model.add(Flatten())
        
        # Add dense layer
        model.add(Dense(
            params['dense_layers']['dense_nodes_1'],
            activation='relu',
            kernel_regularizer=regularizers.l2(params['dense_layers']['kernel_regularizer_1'])
        ))
        model.add(Dropout(params['dense_layers']['dense_drop_out_1']))
        
        # Add output layer
        if multiclass:
            model.add(Dense(len(labels), activation='softmax'))
        else:
            model.add(Dense(len(labels), activation='sigmoid'))
        
        # Configure optimizer
        if params['optimizer'] == 'rmsprop':
            optimizer = optimizers.RMSprop(learning_rate=params['lr'])
        elif params['optimizer'] == 'sgd':
            optimizer = optimizers.SGD(
                learning_rate=params['lr'],
                decay=1e-6,
                momentum=0.9,
                nesterov=True
            )
        else:  # default to adam
            optimizer = optimizers.Adam(learning_rate=params['lr'])
        
        # Compile model
        model.compile(
            loss='categorical_crossentropy' if multiclass else 'binary_crossentropy',
            optimizer=optimizer,
            metrics=['accuracy']
        )
        
        # Log model summary
        model.summary(print_fn=logger.info)
        
        return model
    
    except Exception as e:
        logger.error(f"Error creating model: {str(e)}")
        raise ValueError(f"Failed to create model: {str(e)}")
