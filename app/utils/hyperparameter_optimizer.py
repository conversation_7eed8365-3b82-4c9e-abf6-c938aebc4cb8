#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Hyperparameter optimizer module for the Trend Prediction Service.
This module provides functionality for finding optimal training parameters.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any
from datetime import datetime
import json
import itertools
from concurrent.futures import ThreadPoolExecutor, as_completed

from app.utils.data_processor import DataProcessor
from app.utils.model_builder import create_cnn_model
from sklearn.metrics import accuracy_score, f1_score

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class HyperparameterOptimizer:
    """
    Class for optimizing hyperparameters using grid search and cross-validation.
    """

    def __init__(self):
        """Initialize the hyperparameter optimizer."""
        self.data_processor = DataProcessor()
        self.best_params = None
        self.best_score = 0.0
        self.optimization_history = []

    def optimize(self, df: pd.DataFrame, optimization_type: str = "grid_search",
                 max_trials: int = 20, metric: str = "f1_score") -> Dict[str, Any]:
        """
        Optimize hyperparameters for the CNN model.

        Args:
            df (pd.DataFrame): Training data
            optimization_type (str): Type of optimization ("grid_search" or "random_search")
            max_trials (int): Maximum number of trials
            metric (str): Metric to optimize ("accuracy" or "f1_score")

        Returns:
            Dict[str, Any]: Best parameters and optimization results
        """
        logger.info(f"Starting hyperparameter optimization with {optimization_type}")
        logger.info(f"Max trials: {max_trials}, Metric: {metric}")

        # Define parameter search space
        param_space = self._get_parameter_space()

        if optimization_type == "grid_search":
            param_combinations = self._generate_grid_combinations(param_space, max_trials)
        else:
            param_combinations = self._generate_random_combinations(param_space, max_trials)

        logger.info(f"Generated {len(param_combinations)} parameter combinations")

        # Prepare data once
        x_train, x_validation, y_train, y_validation, labels = self.data_processor.prepare_training_data(df)

        # Optimize parameters
        best_params, best_score, history = self._evaluate_parameters(
            param_combinations, x_train, x_validation, y_train, y_validation, labels, metric
        )

        self.best_params = best_params
        self.best_score = best_score
        self.optimization_history = history

        # Save optimization results
        self._save_optimization_results(best_params, best_score, history)

        return {
            "best_params": best_params,
            "best_score": best_score,
            "optimization_history": history,
            "total_trials": len(history)
        }

    def _get_parameter_space(self) -> Dict[str, List]:
        """
        Define the hyperparameter search space.

        Returns:
            Dict[str, List]: Parameter search space
        """
        return {
            "epochs": [32, 48, 64, 80],
            "batch_size": [40, 60, 80, 100],
            "learning_rate": [0.0005, 0.001, 0.002, 0.005],
            "conv_filters_1": [16, 20, 24, 32],
            "conv_filters_2": [32, 40, 48, 64],
            "dense_nodes": [24, 32, 48, 64],
            "dropout_conv_1": [0.1, 0.15, 0.22, 0.3],
            "dropout_conv_2": [0.05, 0.1, 0.15, 0.2],
            "dropout_dense": [0.15, 0.22, 0.3, 0.4]
        }

    def _generate_grid_combinations(self, param_space: Dict[str, List], max_trials: int) -> List[Dict]:
        """
        Generate parameter combinations for grid search.

        Args:
            param_space (Dict[str, List]): Parameter search space
            max_trials (int): Maximum number of trials

        Returns:
            List[Dict]: List of parameter combinations
        """
        # Generate all possible combinations
        keys = list(param_space.keys())
        values = list(param_space.values())

        all_combinations = []
        for combination in itertools.product(*values):
            param_dict = {k: int(v) if isinstance(v, np.integer) else float(v) if isinstance(v, np.floating) else v
                         for k, v in zip(keys, combination)}
            all_combinations.append(param_dict)

        # Limit to max_trials
        if len(all_combinations) > max_trials:
            # Select combinations with good balance
            step = len(all_combinations) // max_trials
            selected_combinations = all_combinations[::step][:max_trials]
        else:
            selected_combinations = all_combinations

        return selected_combinations

    def _generate_random_combinations(self, param_space: Dict[str, List], max_trials: int) -> List[Dict]:
        """
        Generate random parameter combinations.

        Args:
            param_space (Dict[str, List]): Parameter search space
            max_trials (int): Maximum number of trials

        Returns:
            List[Dict]: List of parameter combinations
        """
        combinations = []

        for _ in range(max_trials):
            param_dict = {}
            for param_name, param_values in param_space.items():
                value = np.random.choice(param_values)
                # Convert numpy types to Python native types for JSON serialization
                if isinstance(value, np.integer):
                    param_dict[param_name] = int(value)
                elif isinstance(value, np.floating):
                    param_dict[param_name] = float(value)
                else:
                    param_dict[param_name] = value
            combinations.append(param_dict)

        return combinations

    def _evaluate_parameters(self, param_combinations: List[Dict], x_train, x_validation,
                           y_train, y_validation, labels, metric: str) -> Tuple[Dict, float, List]:
        """
        Evaluate parameter combinations.

        Args:
            param_combinations (List[Dict]): Parameter combinations to evaluate
            x_train, x_validation, y_train, y_validation, labels: Training data
            metric (str): Metric to optimize

        Returns:
            Tuple[Dict, float, List]: Best parameters, best score, and history
        """
        best_params = None
        best_score = 0.0
        history = []

        for i, params in enumerate(param_combinations):
            logger.info(f"Trial {i+1}/{len(param_combinations)}: {params}")

            try:
                # Create model with current parameters
                model_params = self._convert_to_model_params(params)
                model = create_cnn_model(model_params, x_train, labels, multiclass=False)

                # Train model with reduced epochs for optimization
                train_epochs = min(params["epochs"], 20)  # Limit epochs for faster optimization

                history_obj = model.fit(
                    x_train, y_train,
                    validation_data=(x_validation, y_validation),
                    epochs=train_epochs,
                    batch_size=params["batch_size"],
                    verbose=0
                )

                # Evaluate model
                y_pred = model.predict(x_validation, verbose=0)
                y_pred_classes = np.argmax(y_pred, axis=1)
                y_true_classes = np.argmax(y_validation, axis=1)

                accuracy = accuracy_score(y_true_classes, y_pred_classes)
                f1 = f1_score(y_true_classes, y_pred_classes)

                score = f1 if metric == "f1_score" else accuracy

                # Record trial
                trial_result = {
                    "trial": i + 1,
                    "params": params,
                    "accuracy": float(accuracy),
                    "f1_score": float(f1),
                    "score": float(score),
                    "val_loss": float(min(history_obj.history['val_loss']))
                }
                history.append(trial_result)

                # Update best parameters
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                    logger.info(f"New best score: {best_score:.4f}")

                logger.info(f"Trial {i+1} - Accuracy: {accuracy:.4f}, F1: {f1:.4f}, Score: {score:.4f}")

            except Exception as e:
                logger.error(f"Trial {i+1} failed: {str(e)}")
                trial_result = {
                    "trial": i + 1,
                    "params": params,
                    "error": str(e),
                    "score": 0.0
                }
                history.append(trial_result)

        return best_params, best_score, history

    def _convert_to_model_params(self, opt_params: Dict) -> Dict:
        """
        Convert optimization parameters to model parameters format.

        Args:
            opt_params (Dict): Optimization parameters

        Returns:
            Dict: Model parameters
        """
        return {
            'batch_size': opt_params['batch_size'],
            'epochs': opt_params['epochs'],
            'lr': opt_params['learning_rate'],
            'optimizer': 'adam',
            'conv2d_layers': {
                'conv2d_drop_out_1': opt_params['dropout_conv_1'],
                'filters_1': opt_params['conv_filters_1'],
                'kernel_size_1': 2,
                'conv2d_mp_1': 2,
                'strides_1': 1,
                'kernel_regularizer_1': 0.0,
                'conv2_drop_out_2': opt_params['dropout_conv_2'],
                'filters_2': opt_params['conv_filters_2'],
                'kernel_size_2': 2,
                'conv2d_mp_2': 2,
                'strides_2': 1,
                'kernel_regularizer_2': 0.0
            },
            'dense_layers': {
                'dense_drop_out_1': opt_params['dropout_dense'],
                'dense_nodes_1': opt_params['dense_nodes'],
                'kernel_regularizer_1': 0.0
            }
        }

    def _save_optimization_results(self, best_params: Dict, best_score: float, history: List):
        """
        Save optimization results to file.

        Args:
            best_params (Dict): Best parameters found
            best_score (float): Best score achieved
            history (List): Optimization history
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Convert all numpy types to Python native types for JSON serialization
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(v) for v in obj]
            else:
                return obj

        results = {
            "timestamp": timestamp,
            "best_params": convert_numpy_types(best_params),
            "best_score": float(best_score),
            "optimization_history": convert_numpy_types(history)
        }

        filename = f"hyperparameter_optimization_{timestamp}.json"
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)

        logger.info(f"Optimization results saved to {filename}")

    def get_recommended_params(self) -> Dict[str, Any]:
        """
        Get recommended parameters based on optimization results.

        Returns:
            Dict[str, Any]: Recommended parameters
        """
        if self.best_params is None:
            # Return default optimized parameters
            return {
                "epochs": 64,
                "batch_size": 80,
                "learning_rate": 0.001,
                "conv_filters_1": 20,
                "conv_filters_2": 40,
                "dense_nodes": 32,
                "dropout_conv_1": 0.22,
                "dropout_conv_2": 0.05,
                "dropout_dense": 0.22
            }

        return self.best_params
