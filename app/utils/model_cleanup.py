#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Model cleanup service for the Trend Prediction Service.
This module provides functionality for cleaning up old and unused models.
"""

import os
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import threading

from app.utils.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class ModelCleanupService:
    """
    Service for cleaning up old and unused models.
    """
    
    def __init__(self, cleanup_interval_hours: int = 24):
        """
        Initialize the model cleanup service.
        
        Args:
            cleanup_interval_hours (int): Interval in hours between cleanup runs.
        """
        self.cleanup_interval_hours = cleanup_interval_hours
        self.running = False
        self.thread = None
    
    def start(self):
        """Start the cleanup service in a background thread."""
        if not settings.MODEL_CLEANUP_ENABLED:
            logger.info("Model cleanup service is disabled")
            return
        
        if self.running:
            logger.warning("Model cleanup service is already running")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._run_cleanup_loop, daemon=True)
        self.thread.start()
        logger.info("Model cleanup service started")
    
    def stop(self):
        """Stop the cleanup service."""
        if not self.running:
            logger.warning("Model cleanup service is not running")
            return
        
        self.running = False
        if self.thread:
            self.thread.join(timeout=10)
        logger.info("Model cleanup service stopped")
    
    def _run_cleanup_loop(self):
        """Run the cleanup loop in a background thread."""
        while self.running:
            try:
                self.cleanup_models()
            except Exception as e:
                logger.error(f"Error in model cleanup: {str(e)}")
            
            # Sleep for the specified interval
            for _ in range(self.cleanup_interval_hours * 60 * 60):
                if not self.running:
                    break
                time.sleep(1)
    
    def cleanup_models(self):
        """
        Clean up old and unused models.
        
        This method:
        1. Finds all models for each supported pair
        2. Sorts them by creation date
        3. Keeps the N most recent versions and the best model
        4. Deletes models older than the maximum age
        """
        logger.info("Starting model cleanup")
        
        # Process each supported pair
        for pair in settings.SUPPORTED_PAIRS:
            try:
                self._cleanup_pair_models(pair)
            except Exception as e:
                logger.error(f"Error cleaning up models for {pair}: {str(e)}")
        
        logger.info("Model cleanup completed")
    
    def _cleanup_pair_models(self, pair: str):
        """
        Clean up models for a specific pair.
        
        Args:
            pair (str): Trading pair symbol (e.g., "BTCUSDT").
        """
        logger.info(f"Cleaning up models for {pair}")
        
        # Get the model directory for this pair
        pair_model_dir = settings.get_pair_model_dir(pair)
        if not os.path.exists(pair_model_dir):
            logger.warning(f"Model directory for {pair} does not exist")
            return
        
        # Find all model files and their metadata
        models = []
        for filename in os.listdir(pair_model_dir):
            if filename.endswith(".h5"):
                model_path = os.path.join(pair_model_dir, filename)
                
                # Skip the best model symlink
                if filename == "best_model.h5":
                    continue
                
                # Find corresponding metadata file
                metadata_filename = filename.replace(".h5", "_metadata.json")
                metadata_path = os.path.join(pair_model_dir, metadata_filename)
                
                if os.path.exists(metadata_path):
                    try:
                        with open(metadata_path, "r") as f:
                            metadata = json.load(f)
                        
                        # Extract creation timestamp
                        timestamp = metadata.get("timestamp", "")
                        creation_date = datetime.strptime(timestamp, "%Y%m%d_%H%M%S") if timestamp else None
                        
                        # Extract performance metrics
                        metrics = metadata.get("metrics", {})
                        
                        # Extract usage statistics
                        last_used = metadata.get("last_used", "")
                        usage_count = metadata.get("usage_count", 0)
                        
                        models.append({
                            "path": model_path,
                            "metadata_path": metadata_path,
                            "filename": filename,
                            "creation_date": creation_date,
                            "metrics": metrics,
                            "last_used": last_used,
                            "usage_count": usage_count,
                            "is_best": False  # Will be updated later
                        })
                    except Exception as e:
                        logger.warning(f"Error processing metadata for {filename}: {str(e)}")
                else:
                    # If no metadata file exists, use file creation time
                    creation_time = os.path.getctime(model_path)
                    creation_date = datetime.fromtimestamp(creation_time)
                    
                    models.append({
                        "path": model_path,
                        "metadata_path": None,
                        "filename": filename,
                        "creation_date": creation_date,
                        "metrics": {},
                        "last_used": None,
                        "usage_count": 0,
                        "is_best": False
                    })
        
        if not models:
            logger.info(f"No models found for {pair}")
            return
        
        # Find the best model
        best_model_link = os.path.join(pair_model_dir, "best_model.h5")
        if os.path.exists(best_model_link) and os.path.islink(best_model_link):
            best_model_path = os.path.realpath(best_model_link)
            for model in models:
                if model["path"] == best_model_path:
                    model["is_best"] = True
                    break
        
        # Sort models by creation date (newest first)
        models.sort(key=lambda x: x["creation_date"] if x["creation_date"] else datetime.min, reverse=True)
        
        # Determine which models to keep
        models_to_keep = []
        models_to_delete = []
        
        # Keep the N most recent versions
        recent_models = models[:settings.MODEL_MAX_VERSIONS]
        models_to_keep.extend(recent_models)
        
        # Process remaining models
        for model in models[settings.MODEL_MAX_VERSIONS:]:
            # Keep the best model
            if model["is_best"]:
                models_to_keep.append(model)
                continue
            
            # Delete models older than the maximum age
            max_age_date = datetime.now() - timedelta(days=settings.MODEL_MAX_AGE_DAYS)
            if model["creation_date"] and model["creation_date"] < max_age_date:
                models_to_delete.append(model)
            else:
                models_to_keep.append(model)
        
        # Delete models
        for model in models_to_delete:
            try:
                logger.info(f"Deleting old model: {model['filename']}")
                
                # Delete model file
                if os.path.exists(model["path"]):
                    os.remove(model["path"])
                
                # Delete metadata file
                if model["metadata_path"] and os.path.exists(model["metadata_path"]):
                    os.remove(model["metadata_path"])
                
                # Delete any associated files (e.g., training history)
                base_name = os.path.splitext(model["filename"])[0]
                for filename in os.listdir(pair_model_dir):
                    if filename.startswith(base_name) and filename != model["filename"]:
                        file_path = os.path.join(pair_model_dir, filename)
                        if os.path.isfile(file_path):
                            os.remove(file_path)
            except Exception as e:
                logger.error(f"Error deleting model {model['filename']}: {str(e)}")
        
        logger.info(f"Cleaned up {len(models_to_delete)} models for {pair}, kept {len(models_to_keep)} models")

# Create a singleton instance
model_cleanup_service = ModelCleanupService()

def start_cleanup_service():
    """Start the model cleanup service."""
    model_cleanup_service.start()

def stop_cleanup_service():
    """Stop the model cleanup service."""
    model_cleanup_service.stop()

def run_cleanup_now():
    """Run model cleanup immediately."""
    try:
        model_cleanup_service.cleanup_models()
        return {"status": "success", "message": "Model cleanup completed"}
    except Exception as e:
        logger.error(f"Error in manual model cleanup: {str(e)}")
        return {"status": "error", "message": f"Model cleanup failed: {str(e)}"}
