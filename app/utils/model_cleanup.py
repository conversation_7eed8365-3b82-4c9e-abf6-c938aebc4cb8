#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Model cleanup utility for the Trend Prediction Service.
This module provides functionality for cleaning up old and underperforming models.
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from pathlib import Path

from app.utils.config import settings

logger = logging.getLogger(__name__)


class ModelCleanup:
    """
    Utility class for cleaning up old and underperforming models.
    """

    def __init__(self):
        """Initialize the model cleanup utility."""
        self.models_dir = settings.MODEL_DIR
        self.max_age_days = settings.MODEL_MAX_AGE_DAYS
        self.max_versions = settings.MODEL_MAX_VERSIONS
        self.cleanup_enabled = settings.MODEL_CLEANUP_ENABLED

    def cleanup_all_pairs(self) -> Dict[str, Any]:
        """
        Clean up models for all pairs.

        Returns:
            Dict[str, Any]: Cleanup summary
        """
        if not self.cleanup_enabled:
            logger.info("Model cleanup is disabled")
            return {"status": "disabled", "pairs_cleaned": 0}

        logger.info("Starting model cleanup for all pairs")
        
        cleanup_summary = {
            "status": "completed",
            "pairs_cleaned": 0,
            "models_removed": 0,
            "space_freed_mb": 0,
            "pairs": {}
        }

        # Get all pair directories
        if not os.path.exists(self.models_dir):
            logger.warning(f"Models directory does not exist: {self.models_dir}")
            return cleanup_summary

        for pair_dir in os.listdir(self.models_dir):
            pair_path = os.path.join(self.models_dir, pair_dir)
            
            if os.path.isdir(pair_path) and not pair_dir.startswith('.'):
                try:
                    pair_summary = self.cleanup_pair(pair_dir)
                    cleanup_summary["pairs"][pair_dir] = pair_summary
                    cleanup_summary["pairs_cleaned"] += 1
                    cleanup_summary["models_removed"] += pair_summary["models_removed"]
                    cleanup_summary["space_freed_mb"] += pair_summary["space_freed_mb"]
                except Exception as e:
                    logger.error(f"Error cleaning up pair {pair_dir}: {str(e)}")
                    cleanup_summary["pairs"][pair_dir] = {"error": str(e)}

        logger.info(f"Model cleanup completed. Removed {cleanup_summary['models_removed']} models, "
                   f"freed {cleanup_summary['space_freed_mb']:.2f} MB")
        
        return cleanup_summary

    def cleanup_pair(self, pair: str) -> Dict[str, Any]:
        """
        Clean up models for a specific pair.

        Args:
            pair (str): Trading pair symbol

        Returns:
            Dict[str, Any]: Cleanup summary for the pair
        """
        pair_dir = os.path.join(self.models_dir, pair)
        
        if not os.path.exists(pair_dir):
            logger.warning(f"Pair directory does not exist: {pair_dir}")
            return {"models_removed": 0, "space_freed_mb": 0, "error": "Directory not found"}

        logger.info(f"Cleaning up models for pair: {pair}")

        # Get all model files and metadata
        model_files = self._get_model_files(pair_dir)
        
        if not model_files:
            logger.info(f"No models found for pair {pair}")
            return {"models_removed": 0, "space_freed_mb": 0}

        # Load metadata for all models
        models_with_metadata = self._load_models_metadata(pair_dir, model_files)

        # Determine which models to remove
        models_to_remove = self._select_models_for_removal(models_with_metadata)

        # Remove selected models
        space_freed = 0
        models_removed = 0

        for model_info in models_to_remove:
            try:
                space_freed += self._remove_model_files(model_info)
                models_removed += 1
                logger.info(f"Removed model: {model_info['model_file']}")
            except Exception as e:
                logger.error(f"Error removing model {model_info['model_file']}: {str(e)}")

        return {
            "models_removed": models_removed,
            "space_freed_mb": round(space_freed / (1024 * 1024), 2),
            "models_kept": len(models_with_metadata) - models_removed
        }

    def _get_model_files(self, pair_dir: str) -> List[str]:
        """Get all model files in the pair directory."""
        model_files = []
        
        for file in os.listdir(pair_dir):
            if file.endswith('.h5') and not file.endswith('_best_model.h5') and file != 'best_model.h5':
                model_files.append(file)
        
        return model_files

    def _load_models_metadata(self, pair_dir: str, model_files: List[str]) -> List[Dict[str, Any]]:
        """Load metadata for all models."""
        models_with_metadata = []
        
        for model_file in model_files:
            metadata_file = model_file.replace('.h5', '_metadata.json')
            metadata_path = os.path.join(pair_dir, metadata_file)
            
            if os.path.exists(metadata_path):
                try:
                    with open(metadata_path, 'r') as f:
                        metadata = json.load(f)
                    
                    # Add file information
                    model_path = os.path.join(pair_dir, model_file)
                    file_size = os.path.getsize(model_path)
                    file_mtime = os.path.getmtime(model_path)
                    
                    models_with_metadata.append({
                        'model_file': model_file,
                        'model_path': model_path,
                        'metadata_file': metadata_file,
                        'metadata_path': metadata_path,
                        'metadata': metadata,
                        'file_size': file_size,
                        'file_mtime': file_mtime,
                        'age_days': (datetime.now().timestamp() - file_mtime) / (24 * 3600)
                    })
                except Exception as e:
                    logger.warning(f"Error loading metadata for {model_file}: {str(e)}")
            else:
                logger.warning(f"Metadata file not found for {model_file}")
        
        return models_with_metadata

    def _select_models_for_removal(self, models_with_metadata: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Select which models should be removed based on age and performance."""
        models_to_remove = []
        
        # Sort models by validation loss (best first)
        models_sorted = sorted(models_with_metadata, 
                             key=lambda x: x['metadata']['performance_metrics']['validation_loss'])
        
        # Remove models older than max_age_days
        for model_info in models_with_metadata:
            if model_info['age_days'] > self.max_age_days:
                models_to_remove.append(model_info)
                logger.info(f"Model {model_info['model_file']} marked for removal (age: {model_info['age_days']:.1f} days)")
        
        # Keep only the best N models (excluding those already marked for age)
        remaining_models = [m for m in models_sorted if m not in models_to_remove]
        
        if len(remaining_models) > self.max_versions:
            models_to_remove_by_count = remaining_models[self.max_versions:]
            models_to_remove.extend(models_to_remove_by_count)
            
            for model_info in models_to_remove_by_count:
                logger.info(f"Model {model_info['model_file']} marked for removal (keeping only top {self.max_versions})")
        
        return models_to_remove

    def _remove_model_files(self, model_info: Dict[str, Any]) -> int:
        """Remove model files and return space freed in bytes."""
        space_freed = 0
        
        # Remove model file
        if os.path.exists(model_info['model_path']):
            space_freed += os.path.getsize(model_info['model_path'])
            os.remove(model_info['model_path'])
        
        # Remove metadata file
        if os.path.exists(model_info['metadata_path']):
            space_freed += os.path.getsize(model_info['metadata_path'])
            os.remove(model_info['metadata_path'])
        
        # Remove history file if it exists
        history_file = model_info['model_file'].replace('.h5', '_history.json')
        history_path = os.path.join(os.path.dirname(model_info['model_path']), history_file)
        if os.path.exists(history_path):
            space_freed += os.path.getsize(history_path)
            os.remove(history_path)
        
        return space_freed

    def get_cleanup_status(self) -> Dict[str, Any]:
        """Get current cleanup configuration and statistics."""
        total_models = 0
        total_size_mb = 0
        pairs_info = {}
        
        if os.path.exists(self.models_dir):
            for pair_dir in os.listdir(self.models_dir):
                pair_path = os.path.join(self.models_dir, pair_dir)
                
                if os.path.isdir(pair_path) and not pair_dir.startswith('.'):
                    model_files = self._get_model_files(pair_path)
                    pair_size = sum(os.path.getsize(os.path.join(pair_path, f)) 
                                  for f in model_files if os.path.exists(os.path.join(pair_path, f)))
                    
                    pairs_info[pair_dir] = {
                        "model_count": len(model_files),
                        "size_mb": round(pair_size / (1024 * 1024), 2)
                    }
                    
                    total_models += len(model_files)
                    total_size_mb += pair_size / (1024 * 1024)
        
        return {
            "cleanup_enabled": self.cleanup_enabled,
            "max_age_days": self.max_age_days,
            "max_versions_per_pair": self.max_versions,
            "total_models": total_models,
            "total_size_mb": round(total_size_mb, 2),
            "pairs": pairs_info
        }


def cleanup_models() -> Dict[str, Any]:
    """
    Convenience function to run model cleanup.
    
    Returns:
        Dict[str, Any]: Cleanup summary
    """
    cleanup = ModelCleanup()
    return cleanup.cleanup_all_pairs()


def get_cleanup_status() -> Dict[str, Any]:
    """
    Convenience function to get cleanup status.
    
    Returns:
        Dict[str, Any]: Cleanup status
    """
    cleanup = ModelCleanup()
    return cleanup.get_cleanup_status()
