#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Health check module for the Trend Prediction Service.
This module provides endpoints for checking the health of the service.
"""

import logging
from fastapi import APIRouter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/health",
    tags=["Health"],
    responses={404: {"description": "Not found"}},
)

@router.get("")
async def health_check():
    """
    Check the health of the service.
    
    Returns:
        dict: A dictionary with the status of the service.
    """
    return {"status": "ok"}
