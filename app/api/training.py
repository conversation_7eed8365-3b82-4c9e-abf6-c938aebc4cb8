#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Training API module for the Trend Prediction Service.
This module provides endpoints for monitoring training progress.
"""

import logging
from typing import Dict, List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/training",
    tags=["Training Progress"],
    responses={404: {"description": "Not found"}},
)

# Store for active training sessions
active_trainings = {}

class TrainingStatus(BaseModel):
    """Training status model."""
    is_training: bool = Field(..., description="Whether training is in progress")
    progress: float = Field(..., description="Training progress (0.0 to 1.0)")
    current_epoch: int = Field(..., description="Current training epoch")
    total_epochs: int = Field(..., description="Total number of epochs")
    metrics: Optional[Dict[str, float]] = Field(None, description="Training metrics")
    start_time: Optional[str] = Field(None, description="Training start time")
    end_time: Optional[str] = Field(None, description="Training end time")
    error: Optional[str] = Field(None, description="Error message if training failed")
    pair: Optional[str] = Field(None, description="Trading pair symbol")
    model_path: Optional[str] = Field(None, description="Path to the model being trained")

@router.get("/status", response_model=Dict[str, TrainingStatus])
async def get_training_status(pair: Optional[str] = None):
    """
    Get the status of all active training sessions.
    
    If pair is specified, only the status for that pair will be returned.
    """
    try:
        if pair:
            # Return status for specific pair
            if pair in active_trainings:
                return {pair: active_trainings[pair]}
            else:
                return {}
        else:
            # Return status for all pairs
            return active_trainings
    except Exception as e:
        logger.error(f"Error getting training status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get training status: {str(e)}")

def register_training_session(pair: str, status: Dict):
    """
    Register a new training session.
    
    Args:
        pair (str): Trading pair symbol.
        status (Dict): Initial training status.
    """
    active_trainings[pair] = status

def update_training_status(pair: str, status: Dict):
    """
    Update the status of a training session.
    
    Args:
        pair (str): Trading pair symbol.
        status (Dict): Updated training status.
    """
    if pair in active_trainings:
        active_trainings[pair].update(status)

def remove_training_session(pair: str):
    """
    Remove a training session.
    
    Args:
        pair (str): Trading pair symbol.
    """
    if pair in active_trainings:
        del active_trainings[pair]
