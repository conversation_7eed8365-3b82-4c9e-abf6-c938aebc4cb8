#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main API entry point for the Trend Prediction Service.
This module provides endpoints for making predictions, training models, and backtesting.
"""

import os
import time
import logging
import threading
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel

from app.models.trainer import ModelTrainer
from app.models.predictor import TrendPredictor
from app.models.backtester import ModelBacktester
from app.utils.config import settings
from app.utils.hyperparameter_optimizer import HyperparameterOptimizer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Trend Prediction Service",
    description="API for predicting market trends using CNN models",
    version="1.0.0",
)

# Global training jobs tracker
training_jobs = {}

# Data models
class MarketData(BaseModel):
    timestamp: int
    open: float
    high: float
    low: float
    close: float
    volume: float
    symbol: str = "BTCUSDT"

class PredictionRequest(BaseModel):
    market_data: List[MarketData]

class TrainingRequest(BaseModel):
    data_path: Optional[str] = None
    epochs: int = 64
    batch_size: int = 80
    learning_rate: float = 0.001
    pair: str
    # Automatic optimization parameters
    auto_optimize: bool = True  # Enable automatic hyperparameter optimization
    optimization_trials: int = 12  # Number of Optuna trials
    optimization_timeout: int = 600  # Timeout in seconds (10 minutes)

class TrainingResponse(BaseModel):
    status: str
    training_id: str

class BacktestRequest(BaseModel):
    model_path: str
    data_path: str
    window: int = 24
    generate_plots: bool = False

class OptimizationRequest(BaseModel):
    data_path: Optional[str] = None
    pair: str
    optimization_type: str = "grid_search"  # "grid_search" or "random_search"
    max_trials: int = 20
    metric: str = "f1_score"  # "accuracy" or "f1_score"

class BacktestResponse(BaseModel):
    performance: Dict[str, Any]
    plots: Optional[Dict[str, Any]] = None

def get_model_performance(pair):
    """Get the performance metrics for a model."""
    model_dir = f"models/{pair}"
    if not os.path.exists(model_dir):
        return None

    metadata_files = [f for f in os.listdir(model_dir) if f.endswith('_metadata.json')]
    if not metadata_files:
        return None

    metadata_files.sort(reverse=True)
    latest_metadata = os.path.join(model_dir, metadata_files[0])

    try:
        with open(latest_metadata, 'r') as f:
            metadata = json.load(f)
            return metadata.get('metrics', None)
    except Exception as e:
        logger.error(f"Error loading performance data for {pair}: {e}")

    return None

# API endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

@app.post("/predict")
async def predict(request: PredictionRequest):
    """Make predictions based on market data."""
    predictions = []

    for data in request.market_data:
        try:
            # Try to use real predictor if available
            predictor = TrendPredictor(pair=data.symbol)
            prediction, confidence = predictor.predict([data.dict()])

            predictions.append({
                "timestamp": data.timestamp,
                "symbol": data.symbol,
                "prediction": prediction,
                "confidence": confidence,
            })

        except Exception as e:
            logger.error(f"Real prediction failed for {data.symbol}: {e}")
            # Fallback to simple rule-based prediction
            prediction = 1 if data.close > data.open else 0
            confidence = 0.5

            predictions.append({
                "timestamp": data.timestamp,
                "symbol": data.symbol,
                "prediction": prediction,
                "confidence": confidence,
            })

    return {"predictions": predictions}

def train_model_task(training_id: str, request: TrainingRequest):
    """Background task to train a model."""
    pair = request.pair
    epochs = request.epochs
    batch_size = request.batch_size
    learning_rate = request.learning_rate
    data_path = request.data_path

    # Initialize training job status
    training_jobs[training_id] = {
        "pair": pair,
        "status": "in_progress",
        "progress": 0.0,
        "metrics": {
            "loss": 1.0,
            "accuracy": 0.5,
            "val_loss": 1.0,
            "val_accuracy": 0.5
        },
        "start_time": time.time(),
        "epochs": epochs,
        "current_epoch": 0
    }

    try:
        # Check if data file exists
        if not os.path.exists(data_path):
            raise ValueError(f"Data file not found: {data_path}")

        logger.info(f"Starting real training for {pair}")

        # Initialize trainer
        trainer = ModelTrainer(pair=pair)

        # Prepare training parameters including optimization settings
        params = {
            'epochs': epochs,
            'batch_size': batch_size,
            'learning_rate': learning_rate,
            'optimizer': 'adam',
            'auto_optimize': request.auto_optimize,
            'optimization_trials': request.optimization_trials,
            'optimization_timeout': request.optimization_timeout
        }

        # Prepare data source
        data_source = {'data_path': data_path}

        # Train the model
        result = trainer.train(data_source, params, pair=pair)

        # Update job status to completed
        training_jobs[training_id]["status"] = "completed"
        training_jobs[training_id]["end_time"] = time.time()
        training_jobs[training_id]["progress"] = 1.0
        training_jobs[training_id]["current_epoch"] = epochs
        training_jobs[training_id]["metrics"] = result["metrics"]

        logger.info(f"Real training completed for {pair} with training_id {training_id}")
        logger.info(f"Model saved to {result['model_path']}")
        logger.info(f"Metrics: {result['metrics']}")

    except Exception as e:
        logger.error(f"Training failed for {pair}: {e}")
        training_jobs[training_id]["status"] = "failed"
        training_jobs[training_id]["error"] = str(e)
        training_jobs[training_id]["end_time"] = time.time()

@app.post("/train", response_model=TrainingResponse)
async def train_model(request: TrainingRequest, background_tasks: BackgroundTasks):
    """Train a new model with automatic dataset detection."""
    logger.info(f"Training requested for {request.pair}")

    # Auto-detect data file if not provided or doesn't exist
    data_path = request.data_path
    if not data_path or not os.path.exists(data_path):
        # Try to find data file automatically
        auto_data_path = f"data/{request.pair}_1h.csv"
        if os.path.exists(auto_data_path):
            data_path = auto_data_path
            logger.info(f"Auto-detected data file: {data_path}")
        else:
            # Check for any CSV file with the pair name
            data_dir = "data"
            if os.path.exists(data_dir):
                for file in os.listdir(data_dir):
                    if file.startswith(request.pair) and file.endswith('.csv'):
                        data_path = os.path.join(data_dir, file)
                        logger.info(f"Auto-detected data file: {data_path}")
                        break

    # Final check if data file exists
    if not data_path or not os.path.exists(data_path):
        raise HTTPException(
            status_code=404,
            detail=f"Data file not found. Please upload {request.pair}_1h.csv to the data/ directory"
        )

    # Create model directory for new pairs
    model_dir = f"models/{request.pair}"
    os.makedirs(model_dir, exist_ok=True)
    logger.info(f"Model directory ready: {model_dir}")

    # Update request with correct data path
    request.data_path = data_path

    # Generate a unique training ID
    training_id = f"training-{int(time.time())}"

    # Start training in the background
    background_tasks.add_task(train_model_task, training_id, request)

    return {
        "status": "started",
        "training_id": training_id,
        "data_path": data_path,
        "model_directory": model_dir
    }

@app.get("/training/status")
async def get_training_status(pair: str):
    """Get the status of the most recent training job for a pair."""
    # Find the most recent training job for this pair
    relevant_jobs = [job_id for job_id, job in training_jobs.items()
                    if job["pair"] == pair]

    if not relevant_jobs:
        raise HTTPException(status_code=404, detail=f"No training jobs found for pair {pair}")

    # Sort by start time (most recent first)
    relevant_jobs.sort(key=lambda job_id: training_jobs[job_id]["start_time"], reverse=True)
    latest_job_id = relevant_jobs[0]
    job = training_jobs[latest_job_id]

    # Calculate elapsed time
    elapsed_time = time.time() - job["start_time"]

    return {
        "status": job["status"],
        "progress": job["progress"],
        "metrics": job["metrics"],
        "current_epoch": job["current_epoch"],
        "total_epochs": job["epochs"],
        "elapsed_time": elapsed_time
    }

@app.post("/backtest", response_model=BacktestResponse)
async def backtest_model(request: BacktestRequest):
    """Backtest a model on historical data."""
    logger.info(f"Backtesting requested for model: {request.model_path}")

    try:
        # Use the real backtester
        backtester = ModelBacktester()

        # Perform backtesting
        data_source = {'data_path': request.data_path}
        result = backtester.backtest(
            model_path=request.model_path,
            data_source=data_source,
            window=request.window,
            generate_plots=request.generate_plots
        )

        return {
            "performance": result["metrics"],
            "plots": result.get("plots", None)
        }

    except Exception as e:
        logger.error(f"Real backtesting failed: {e}")
        raise HTTPException(status_code=500, detail=f"Backtesting failed: {str(e)}")

@app.get("/datasets")
async def list_datasets():
    """List all available datasets."""
    datasets = []
    data_dir = "data"

    if os.path.exists(data_dir):
        for file in os.listdir(data_dir):
            if file.endswith('.csv'):
                file_path = os.path.join(data_dir, file)
                file_size = os.path.getsize(file_path)
                last_modified = datetime.fromtimestamp(os.path.getmtime(file_path))

                # Extract pair name from filename
                pair_name = file.replace('_1h.csv', '').replace('.csv', '')

                datasets.append({
                    "pair": pair_name,
                    "filename": file,
                    "file_path": file_path,
                    "size_bytes": file_size,
                    "size_mb": round(file_size / (1024 * 1024), 2),
                    "last_modified": last_modified.isoformat()
                })

    return {"datasets": datasets}

@app.get("/models")
async def list_models():
    """List all available models with their performance metrics."""
    models = []

    # Check all model directories, not just supported pairs
    models_dir = "models"
    if os.path.exists(models_dir):
        for pair_dir in os.listdir(models_dir):
            pair_path = os.path.join(models_dir, pair_dir)
            if os.path.isdir(pair_path):
                # Check for best model
                best_model_path = os.path.join(pair_path, "best_model.h5")
                if os.path.exists(best_model_path):
                    # Get last modified time
                    last_updated = datetime.fromtimestamp(os.path.getmtime(best_model_path))

                    # Get performance metrics
                    performance = get_model_performance(pair_dir)

                    models.append({
                        "pair": pair_dir,
                        "model_path": f"models/{pair_dir}/best_model.h5",
                        "last_updated": last_updated.isoformat(),
                        "performance": performance
                    })

    return {"models": models}

@app.post("/optimize")
async def optimize_hyperparameters(request: OptimizationRequest, background_tasks: BackgroundTasks):
    """
    Optimize hyperparameters for the specified trading pair.
    """
    try:
        # Auto-detect data path if not provided
        if not request.data_path:
            data_path = f"data/{request.pair}_1h.csv"
        else:
            data_path = request.data_path

        # Check if data file exists
        if not os.path.exists(data_path):
            raise HTTPException(
                status_code=404,
                detail=f"Data file not found: {data_path}"
            )

        logger.info(f"Starting hyperparameter optimization for {request.pair}")

        # Load data
        import pandas as pd
        df = pd.read_csv(data_path)

        # Initialize optimizer
        optimizer = HyperparameterOptimizer()

        # Run optimization
        results = optimizer.optimize(
            df=df,
            optimization_type=request.optimization_type,
            max_trials=request.max_trials,
            metric=request.metric
        )

        logger.info(f"Optimization completed for {request.pair}")
        logger.info(f"Best score: {results['best_score']:.4f}")
        logger.info(f"Best params: {results['best_params']}")

        return {
            "status": "completed",
            "pair": request.pair,
            "best_score": results['best_score'],
            "best_params": results['best_params'],
            "total_trials": results['total_trials'],
            "optimization_type": request.optimization_type,
            "metric": request.metric
        }

    except Exception as e:
        logger.error(f"Optimization error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Optimization failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.api.main:app", host="0.0.0.0", port=8000, reload=True)
