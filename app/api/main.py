#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main API entry point for the Trend Prediction Service.
This module provides endpoints for making predictions and retraining the model.
"""

import os
import logging
from typing import Dict, List, Optional, Union
from datetime import datetime

from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field

from app.models.predictor import TrendPredictor
from app.models.trainer import ModelTrainer
from app.models.backtester import ModelBacktester
from app.utils.config import settings
from app.api.training import router as training_router
from app.api.health import router as health_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Trend Prediction Service",
    description="API for predicting market trends using CNN models",
    version="1.0.0",
)

# Include routers
app.include_router(training_router)
app.include_router(health_router)

# Initialize model predictors and backtester
predictors = {}
for pair in settings.SUPPORTED_PAIRS:
    try:
        pair_model_path = settings.get_default_model_path(pair)
        predictors[pair] = TrendPredictor(model_path=pair_model_path, pair=pair)
    except Exception as e:
        logger.warning(f"Failed to initialize predictor for {pair}: {str(e)}")

# Initialize backtester
backtester = ModelBacktester()

# Initialize model cleanup service
from app.utils.model_cleanup import start_cleanup_service
start_cleanup_service()

# Data models for API
class MarketData(BaseModel):
    """Market data for a single time period."""
    timestamp: int = Field(..., description="Unix timestamp in milliseconds")
    open: float = Field(..., description="Opening price")
    high: float = Field(..., description="Highest price")
    low: float = Field(..., description="Lowest price")
    close: float = Field(..., description="Closing price")
    volume: float = Field(..., description="Trading volume")
    num_trades: Optional[int] = Field(None, description="Number of trades")
    symbol: str = Field(settings.DEFAULT_PAIR, description="Trading pair symbol")

class PredictionRequest(BaseModel):
    """Request model for prediction endpoint."""
    market_data: List[MarketData] = Field(..., description="List of market data points")

class PredictionResponse(BaseModel):
    """Response model for prediction endpoint."""
    prediction: int = Field(..., description="Prediction class (0: down, 1: up, -1: error)")
    probability: float = Field(..., description="Prediction probability")
    timestamp: datetime = Field(default_factory=datetime.now, description="Prediction timestamp")
    symbol: str = Field(..., description="Trading pair symbol")
    error: Optional[str] = Field(None, description="Error message if prediction failed")

class TrainingRequest(BaseModel):
    """Request model for training endpoint."""
    market_data: Optional[List[MarketData]] = Field(None, description="List of market data points for training")
    data_path: Optional[str] = Field(None, description="Path to CSV file with training data")
    epochs: int = Field(64, description="Number of training epochs")
    batch_size: int = Field(80, description="Batch size for training")
    learning_rate: float = Field(0.001, description="Learning rate for the optimizer")
    save_path: Optional[str] = Field(None, description="Path to save the trained model")
    pair: Optional[str] = Field(None, description="Trading pair symbol (e.g., 'BTCUSDT')")

class TrainingResponse(BaseModel):
    """Response model for training endpoint."""
    status: str = Field(..., description="Training status")
    model_path: Optional[str] = Field(None, description="Path to the trained model")
    metrics: Optional[Dict[str, float]] = Field(None, description="Training metrics")
    timestamp: datetime = Field(default_factory=datetime.now, description="Training timestamp")
    pair: Optional[str] = Field(None, description="Trading pair symbol")
    progress: Optional[float] = Field(None, description="Training progress (0.0 to 1.0)")
    error: Optional[str] = Field(None, description="Error message if training failed")

class BacktestRequest(BaseModel):
    """Request model for backtesting endpoint."""
    model_path: Optional[str] = Field(None, description="Path to the model file")
    market_data: Optional[List[MarketData]] = Field(None, description="List of market data points for backtesting")
    data_path: Optional[str] = Field(None, description="Path to CSV file with backtesting data")
    window: int = Field(24, description="Prediction window in hours")
    generate_plots: bool = Field(True, description="Whether to generate performance plots")

class BacktestResponse(BaseModel):
    """Response model for backtesting endpoint."""
    status: str = Field(..., description="Backtesting status")
    metrics: Optional[Dict[str, float]] = Field(None, description="Performance metrics")
    plots: Optional[Dict[str, str]] = Field(None, description="Base64-encoded plot images")
    timestamp: datetime = Field(default_factory=datetime.now, description="Backtesting timestamp")

class ModelComparisonRequest(BaseModel):
    """Request model for model comparison endpoint."""
    model_paths: List[str] = Field(..., description="List of model paths to compare")
    data_path: Optional[str] = Field(None, description="Path to CSV file with backtesting data")
    market_data: Optional[List[MarketData]] = Field(None, description="List of market data points for backtesting")
    window: int = Field(24, description="Prediction window in hours")

class HealthResponse(BaseModel):
    """Response model for health check endpoint."""
    status: str = Field("ok", description="API status")
    version: str = Field(..., description="API version")
    timestamp: datetime = Field(default_factory=datetime.now, description="Health check timestamp")

# API endpoints
@app.get("/health", response_model=HealthResponse, tags=["Health"])
async def health_check():
    """Health check endpoint."""
    return HealthResponse(version=app.version)

@app.post("/predict", response_model=PredictionResponse, tags=["Prediction"])
async def predict(request: PredictionRequest):
    """
    Make a prediction based on the provided market data.

    This endpoint accepts market data and returns a prediction of the market trend.
    The trading pair is determined from the symbol field in the market data.
    """
    try:
        # Extract market data
        market_data = request.market_data

        if not market_data:
            raise HTTPException(status_code=400, detail="No market data provided")

        # Get the symbol from the first data point
        symbol = market_data[0].symbol

        # Check if the pair is supported
        if not settings.is_pair_supported(symbol):
            return PredictionResponse(
                prediction=-1,  # -1 indicates unsupported pair
                probability=0.0,
                symbol=symbol,
                error=f"Pair {symbol} is not supported. Call the training endpoint to train a model for this pair."
            )

        # Get the predictor for this pair
        if symbol not in predictors:
            # Try to initialize a predictor for this pair
            try:
                pair_model_path = settings.get_default_model_path(symbol)
                if os.path.exists(pair_model_path):
                    predictors[symbol] = TrendPredictor(model_path=pair_model_path, pair=symbol)
                else:
                    return PredictionResponse(
                        prediction=-1,  # -1 indicates no model available
                        probability=0.0,
                        symbol=symbol,
                        error=f"No model available for pair {symbol}. Call the training endpoint to train a model for this pair."
                    )
            except Exception as e:
                logger.error(f"Failed to initialize predictor for {symbol}: {str(e)}")
                return PredictionResponse(
                    prediction=-1,
                    probability=0.0,
                    symbol=symbol,
                    error=f"Failed to initialize predictor for {symbol}: {str(e)}"
                )

        # Convert to format expected by predictor
        data_dict = [data.dict() for data in market_data]

        # Make prediction
        prediction, probability = predictors[symbol].predict(data_dict)

        return PredictionResponse(
            prediction=prediction,
            probability=probability,
            symbol=symbol
        )
    except Exception as e:
        logger.error(f"Prediction error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.post("/train", response_model=TrainingResponse, tags=["Training"])
async def train_model(request: TrainingRequest, background_tasks: BackgroundTasks):
    """
    Train a new model with the provided market data.

    This endpoint accepts market data and trains a new model. The training is performed
    in the background, and the endpoint returns immediately with a status message.

    If no pair is specified, it will be determined from the market data. If no market data
    is provided, the default pair will be used.
    """
    try:
        # Determine the pair
        pair = request.pair

        if not pair and request.market_data and len(request.market_data) > 0:
            # Get pair from market data
            pair = request.market_data[0].symbol

        if not pair:
            pair = settings.DEFAULT_PAIR

        # Check if pair is supported
        if not settings.is_pair_supported(pair):
            # If not in the supported list, add it dynamically
            settings.SUPPORTED_PAIRS.append(pair)
            # Create directory for the new pair
            pair_model_dir = settings.get_pair_model_dir(pair)
            os.makedirs(pair_model_dir, exist_ok=True)
            logger.info(f"Added new pair: {pair}")

        # Initialize trainer with the specified pair
        trainer = ModelTrainer(pair=pair)

        # Prepare training parameters
        training_params = {
            "epochs": request.epochs,
            "batch_size": request.batch_size,
            "learning_rate": request.learning_rate,
        }

        # Determine data source
        if request.market_data:
            # Convert to format expected by trainer
            data_dict = [data.dict() for data in request.market_data]
            data_source = {"market_data": data_dict}
        elif request.data_path:
            data_source = {"data_path": request.data_path}
        else:
            raise HTTPException(
                status_code=400,
                detail="Either market_data or data_path must be provided"
            )

        # Determine save path
        save_path = request.save_path
        if not save_path:
            save_path = settings.get_pair_model_dir(pair)

        # Start training in background
        background_tasks.add_task(
            trainer.train,
            data_source=data_source,
            params=training_params,
            save_path=save_path,
            pair=pair
        )

        return TrainingResponse(
            status="Training started in the background",
            model_path=None,
            metrics=None,
            pair=pair,
            progress=0.0
        )
    except Exception as e:
        logger.error(f"Training error: {str(e)}")
        return TrainingResponse(
            status="Training failed",
            model_path=None,
            metrics=None,
            pair=request.pair,
            error=str(e)
        )

@app.get("/models", tags=["Models"])
async def list_models(pair: Optional[str] = None):
    """List all available trained models.

    If pair is specified, only models for that pair will be listed.
    Otherwise, models for all supported pairs will be listed.
    """
    try:
        result = {}

        # Determine which pairs to list
        pairs_to_list = [pair] if pair else settings.SUPPORTED_PAIRS

        for p in pairs_to_list:
            if not settings.is_pair_supported(p):
                continue

            pair_model_dir = settings.get_pair_model_dir(p)
            if os.path.exists(pair_model_dir):
                # List all model files for this pair
                models = [f for f in os.listdir(pair_model_dir)
                         if f.endswith('.h5') and not f.endswith('.placeholder')
                         and f != 'best_model.h5']

                # Get best model if it exists
                best_model_path = os.path.join(pair_model_dir, 'best_model.h5')
                best_model = None
                if os.path.exists(best_model_path) and os.path.islink(best_model_path):
                    best_model = os.path.basename(os.path.realpath(best_model_path))

                result[p] = {
                    "models": models,
                    "best_model": best_model,
                    "count": len(models)
                }

        return result
    except Exception as e:
        logger.error(f"Error listing models: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list models: {str(e)}")

@app.post("/models/select", tags=["Models"])
async def select_model(model_name: str, pair: str):
    """Select a specific model for predictions.

    Args:
        model_name: Name of the model file to select
        pair: Trading pair symbol (e.g., "BTCUSDT")
    """
    try:
        # Check if pair is supported
        if not settings.is_pair_supported(pair):
            raise HTTPException(status_code=400, detail=f"Pair {pair} is not supported")

        # Get the model directory for this pair
        pair_model_dir = settings.get_pair_model_dir(pair)
        model_path = os.path.join(pair_model_dir, model_name)

        if not os.path.exists(model_path):
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found for pair {pair}")

        # Initialize predictor for this pair if it doesn't exist
        if pair not in predictors:
            predictors[pair] = TrendPredictor(pair=pair)

        # Update the predictor with the new model
        predictors[pair].load_model(model_path)

        return {"status": "success", "message": f"Model {model_name} loaded successfully for pair {pair}"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error selecting model: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to select model: {str(e)}")

@app.post("/backtest", response_model=BacktestResponse, tags=["Performance"])
async def backtest_model(request: BacktestRequest, background_tasks: BackgroundTasks):
    """
    Backtest a model on historical data.

    This endpoint evaluates model performance on historical data and returns
    performance metrics and visualizations.
    """
    try:
        # Determine model path
        model_path = request.model_path
        if not model_path:
            model_path = settings.DEFAULT_MODEL_PATH

        if not os.path.exists(model_path):
            raise HTTPException(status_code=404, detail=f"Model not found at {model_path}")

        # Determine data source
        if request.market_data:
            # Convert to format expected by backtester
            data_dict = [data.dict() for data in request.market_data]
            data_source = {"market_data": data_dict}
        elif request.data_path:
            data_source = {"data_path": request.data_path}
        else:
            raise HTTPException(
                status_code=400,
                detail="Either market_data or data_path must be provided"
            )

        # Start backtesting in background
        background_tasks.add_task(
            backtester.backtest,
            model_path=model_path,
            data_source=data_source,
            window=request.window,
            generate_plots=request.generate_plots
        )

        return BacktestResponse(
            status="Backtesting started in the background",
            metrics=None,
            plots=None
        )
    except Exception as e:
        logger.error(f"Backtesting error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Backtesting failed: {str(e)}")

@app.get("/backtest/results", tags=["Performance"])
async def get_backtest_results(results_id: Optional[str] = None):
    """
    Get backtesting results.

    This endpoint returns the results of previous backtesting runs.
    If results_id is provided, returns only the specified results.
    """
    try:
        results = backtester.get_backtest_results(results_id)

        # Remove large plot data from response
        for result in results:
            if 'plots' in result:
                result['plots'] = {k: "[Base64 encoded image]" for k in result['plots'].keys()}

        return {"results": results}
    except Exception as e:
        logger.error(f"Error getting backtest results: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get backtest results: {str(e)}")

@app.post("/backtest/compare", tags=["Performance"])
async def compare_models(request: ModelComparisonRequest, background_tasks: BackgroundTasks):
    """
    Compare multiple models on the same dataset.

    This endpoint compares the performance of multiple models on the same dataset
    and returns the results.
    """
    try:
        # Validate model paths
        for model_path in request.model_paths:
            if not os.path.exists(model_path):
                raise HTTPException(status_code=404, detail=f"Model not found at {model_path}")

        # Determine data source
        if request.market_data:
            # Convert to format expected by backtester
            data_dict = [data.dict() for data in request.market_data]
            data_source = {"market_data": data_dict}
        elif request.data_path:
            data_source = {"data_path": request.data_path}
        else:
            raise HTTPException(
                status_code=400,
                detail="Either market_data or data_path must be provided"
            )

        # Start comparison in background
        background_tasks.add_task(
            backtester.compare_models,
            model_paths=request.model_paths,
            data_source=data_source,
            window=request.window
        )

        return {
            "status": "Model comparison started in the background",
            "message": f"Comparing {len(request.model_paths)} models"
        }
    except Exception as e:
        logger.error(f"Model comparison error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Model comparison failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.api.main:app", host="0.0.0.0", port=8000, reload=True)
