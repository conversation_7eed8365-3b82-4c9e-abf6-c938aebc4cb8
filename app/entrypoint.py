#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Entry point for the Trend Prediction Service.
This module starts the minimal FastAPI application.
"""

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Start the FastAPI application
logger.info("Starting minimal FastAPI application")
import uvicorn

if __name__ == "__main__":
    uvicorn.run("app.api.minimal:app", host="0.0.0.0", port=8000, reload=False)
