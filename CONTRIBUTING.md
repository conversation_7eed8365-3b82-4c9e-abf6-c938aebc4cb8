# Contributing to Cryptocurrency Trend Prediction Service

Thank you for your interest in contributing to the Cryptocurrency Trend Prediction Service. This document provides guidelines and information for contributors.

## 📋 Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Contribution Guidelines](#contribution-guidelines)
- [Code Standards](#code-standards)
- [Testing Requirements](#testing-requirements)
- [Pull Request Process](#pull-request-process)
- [Issue Reporting](#issue-reporting)

## 📜 Code of Conduct

This project adheres to a professional code of conduct. By participating, you are expected to uphold this code:

- Use welcoming and inclusive language
- Be respectful of differing viewpoints and experiences
- Gracefully accept constructive criticism
- Focus on what is best for the community
- Show empathy towards other community members

## 🚀 Getting Started

### Prerequisites

- Python 3.9 or higher
- Docker and Docker Compose
- Git
- Basic understanding of machine learning and financial markets

### Development Environment

1. **Fork and Clone**
   ```bash
   git clone https://github.com/yourusername/predictmarket-TrendPredictionService.git
   cd predictmarket-TrendPredictionService
   ```

2. **Set up Development Environment**
   ```bash
   # Create virtual environment
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   
   # Install dependencies
   pip install -r requirements.txt
   ```

3. **Run Tests**
   ```bash
   # Build and start services
   docker-compose up --build -d
   
   # Run test suite
   docker exec trend-prediction-service python -m pytest
   ```

## 🛠️ Development Setup

### Local Development

```bash
# Start development environment
docker-compose -f docker-compose.arm64.yml up --build -d

# Access logs
docker logs -f trend-prediction-service

# Access container for debugging
docker exec -it trend-prediction-service bash
```

### Code Structure

```
app/
├── api/                    # FastAPI endpoints
├── models/                 # ML models and training
├── utils/                  # Utilities and helpers
├── core/                   # Configuration
└── main.py                # Application entry point
```

## 📝 Contribution Guidelines

### Types of Contributions

We welcome the following types of contributions:

1. **Bug Fixes**: Fix identified issues
2. **Feature Enhancements**: Improve existing functionality
3. **New Features**: Add new capabilities
4. **Documentation**: Improve or add documentation
5. **Performance Optimizations**: Enhance system performance
6. **Testing**: Add or improve test coverage

### Before Contributing

1. **Check Existing Issues**: Look for existing issues or discussions
2. **Create an Issue**: For new features or significant changes
3. **Discuss Approach**: Engage with maintainers before major work
4. **Follow Standards**: Adhere to coding and documentation standards

## 🎯 Code Standards

### Python Code Style

- **PEP 8**: Follow Python PEP 8 style guidelines
- **Type Hints**: Use type hints for function parameters and returns
- **Docstrings**: Include comprehensive docstrings for all functions
- **Comments**: Add meaningful comments for complex logic

### Example Code Style

```python
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

def process_market_data(
    data: List[Dict[str, float]], 
    window_size: int = 24
) -> Optional[Dict[str, float]]:
    """
    Process market data for trend prediction.
    
    Args:
        data: List of OHLCV data dictionaries
        window_size: Size of the analysis window
        
    Returns:
        Processed data dictionary or None if invalid
        
    Raises:
        ValueError: If data is insufficient or invalid
    """
    if len(data) < window_size:
        logger.warning(f"Insufficient data: {len(data)} < {window_size}")
        return None
        
    # Processing logic here
    return processed_data
```

### File Organization

- **Imports**: Group imports (standard, third-party, local)
- **Constants**: Define constants at module level
- **Classes**: One main class per file when possible
- **Functions**: Logical grouping and clear naming

## 🧪 Testing Requirements

### Test Coverage

- **Unit Tests**: Test individual functions and methods
- **Integration Tests**: Test component interactions
- **API Tests**: Test all API endpoints
- **Performance Tests**: Test system performance

### Running Tests

```bash
# Run all tests
docker exec trend-prediction-service python -m pytest

# Run specific test file
docker exec trend-prediction-service python -m pytest tests/test_models.py

# Run with coverage
docker exec trend-prediction-service python -m pytest --cov=app

# Run performance tests
docker exec trend-prediction-service python -m pytest tests/performance/
```

### Test Structure

```python
import pytest
from app.models.predictor import TrendPredictor

class TestTrendPredictor:
    """Test suite for TrendPredictor class."""
    
    @pytest.fixture
    def sample_data(self):
        """Provide sample data for testing."""
        return [
            {"timestamp": 1625097600000, "open": 35000.0, "high": 36000.0, 
             "low": 34500.0, "close": 35500.0, "volume": 1000.0}
        ]
    
    def test_prediction_format(self, sample_data):
        """Test that predictions return correct format."""
        predictor = TrendPredictor()
        result = predictor.predict(sample_data)
        
        assert isinstance(result, dict)
        assert "prediction" in result
        assert "confidence" in result
```

## 🔄 Pull Request Process

### Before Submitting

1. **Update Documentation**: Update relevant documentation
2. **Add Tests**: Include tests for new functionality
3. **Run Test Suite**: Ensure all tests pass
4. **Check Code Style**: Verify code follows style guidelines
5. **Update Changelog**: Add entry to CHANGELOG.md

### Pull Request Template

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
- [ ] Changelog updated
```

### Review Process

1. **Automated Checks**: CI/CD pipeline runs automatically
2. **Code Review**: Maintainer reviews code and approach
3. **Testing**: Comprehensive testing in review environment
4. **Approval**: Approval required from project maintainer
5. **Merge**: Squash and merge after approval

## 🐛 Issue Reporting

### Bug Reports

Include the following information:

- **Environment**: OS, Python version, Docker version
- **Steps to Reproduce**: Clear reproduction steps
- **Expected Behavior**: What should happen
- **Actual Behavior**: What actually happens
- **Logs**: Relevant log output
- **Screenshots**: If applicable

### Feature Requests

Include the following information:

- **Use Case**: Why is this feature needed
- **Proposed Solution**: How should it work
- **Alternatives**: Other solutions considered
- **Additional Context**: Any other relevant information

## 📞 Getting Help

- **Documentation**: Check README.md and API documentation
- **Issues**: Search existing issues for solutions
- **Discussions**: Use GitHub Discussions for questions
- **Contact**: Reach out to project maintainers

## 🏆 Recognition

Contributors will be recognized in:

- **Contributors Section**: Added to README.md
- **Release Notes**: Mentioned in release announcements
- **Changelog**: Credited for specific contributions

Thank you for contributing to the Cryptocurrency Trend Prediction Service!
