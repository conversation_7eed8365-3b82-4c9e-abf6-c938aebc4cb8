#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test runner for the Trend Prediction Service.
This script runs all the unit tests in the app/tests directory.
"""

import unittest
import sys
import os

if __name__ == "__main__":
    # Add the project root to the Python path
    sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
    
    # Discover and run all tests
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover("app/tests", pattern="test_*.py")
    
    # Run the tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Exit with non-zero code if tests failed
    sys.exit(not result.wasSuccessful())
