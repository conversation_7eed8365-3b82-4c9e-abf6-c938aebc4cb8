FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Set environment variable to disable TensorFlow AVX/FMA warnings
ENV TF_CPP_MIN_LOG_LEVEL=2

# Copy application code
COPY app/ ./app/
COPY data/ ./data/

# Create directories
RUN mkdir -p models

# Create directories for each supported pair
RUN mkdir -p models/BTCUSDT models/ETHUSDT

# Set environment variables
ENV PYTHONPATH=/app
ENV TF_CPP_MIN_LOG_LEVEL=2
ENV MODEL_DIR=/app/models
ENV DATA_DIR=/app/data

# Expose port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "app.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
