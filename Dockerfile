# Multi-stage build for production optimization
FROM tensorflow/tensorflow:2.10.1 as base

# Metadata
LABEL maintainer="Cryptocurrency Trend Prediction Service"
LABEL version="1.0.0"
LABEL description="Production-ready cryptocurrency trend prediction service"

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy requirements file
COPY requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Copy application code
COPY app/ ./app/
COPY data/ ./data/

# Create directories with proper permissions
RUN mkdir -p models logs \
    && mkdir -p models/BTCUSDT models/ETHUSDT \
    && chown -R appuser:appuser /app

# Set environment variables
ENV PYTHONPATH=/app
ENV TF_CPP_MIN_LOG_LEVEL=2
ENV MODEL_DIR=/app/models
ENV DATA_DIR=/app/data
ENV LOG_LEVEL=INFO
ENV WORKERS=1

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the application with production settings
CMD ["uvicorn", "app.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1", "--log-level", "info"]
