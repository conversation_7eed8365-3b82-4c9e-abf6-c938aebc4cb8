# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.coverage.*

# Virtual Environment
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Documentation (not needed in container)
README.md
CHANGELOG.md
CONTRIBUTING.md
SECURITY.md
LICENSE
*.md

# Test files and scripts
run_tests.py
test_*.py
validate_*.py
app/tests/
tests/

# Large files and directories
models/**/*.h5
models/**/*.json
models/**/*.pkl
models/**/*.model
predictions/
logs/
cache/
temp/
tmp/

# Development files
docker-compose*.yml
Dockerfile*
.dockerignore

# OS specific
.DS_Store
Thumbs.db

# Backup files
*.bak
*.backup
*~

# Security sensitive
.env
.env.*
secrets/
*.key
*.pem
