# Cryptocurrency Trend Prediction Service - .gitignore

# ===== PYTHON =====
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ===== MACHINE LEARNING =====
# Model files
*.h5
*.hdf5
*.pkl
*.pickle
*.joblib
*.model
*.weights

# TensorFlow
*.pb
*.pbtxt
checkpoint
*.ckpt*
*.meta
*.index

# PyTorch
*.pth
*.pt

# Scikit-learn
*.skl

# Optuna
optuna.db
*.db

# MLflow
mlruns/
mlartifacts/

# Weights & Biases
wandb/

# ===== DATA FILES =====
# Large data files
*.csv
*.tsv
*.json
*.parquet
*.feather
*.xlsx
*.xls

# Compressed files
*.zip
*.tar.gz
*.rar
*.7z

# ===== PROJECT SPECIFIC =====
# Model directories
models/
!models/.gitkeep

# Data directories (keep structure, ignore content)
data/*.csv
data/*.json
data/*.parquet
!data/.gitkeep
!data/README.md

# Logs
logs/
*.log

# Temporary files
temp/
tmp/
cache/

# Backup files
*.bak
*.backup
*~

# ===== DOCKER =====
# Docker volumes
docker-volumes/

# ===== IDE & EDITORS =====
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== OS =====
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== SECURITY =====
# API keys and secrets
.env
.env.local
.env.*.local
secrets.json
config/secrets/
*.key
*.pem
*.crt
*.csr

# ===== MISC =====
# Node modules (if any)
node_modules/

# Sass
.sass-cache/

# Parcel
.parcel-cache/

# Next.js
.next/

# Nuxt.js
.nuxt/

# Gatsby
.cache/
public/

# Storybook
.out/
.storybook-out/

# ===== CUSTOM PROJECT EXCLUSIONS =====
# Exclude sample data but keep structure
data/sample_*
data/test_*
data/demo_*

# Exclude development configs
config/dev/
config/local/

# Exclude performance test results
performance_results/
benchmark_results/

# Exclude documentation builds
docs/build/
docs/_build/

# Keep important empty directories
!models/.gitkeep
!data/.gitkeep
!logs/.gitkeep
