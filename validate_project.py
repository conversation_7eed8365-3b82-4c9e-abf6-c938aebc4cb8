#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Project validation script for the Trend Prediction Service.
This script validates all aspects of the project for production readiness.
"""

import os
import json
import pandas as pd
from typing import List, Dict, Any

def validate_project_structure() -> Dict[str, Any]:
    """Validate the project structure."""
    print("🔍 Validating project structure...")
    
    required_files = [
        "app/api/main.py",
        "app/models/trainer.py",
        "app/models/predictor.py",
        "app/models/backtester.py",
        "app/utils/data_processor.py",
        "app/utils/model_builder.py",
        "app/utils/indicators.py",
        "app/utils/optuna_optimizer.py",
        "app/utils/model_cleanup.py",
        "app/utils/config.py",
        "requirements.txt",
        "Dockerfile",
        "docker-compose.yml",
        "docker-compose.arm64.yml",
        "README.md"
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
        else:
            missing_files.append(file_path)
    
    return {
        "status": "passed" if not missing_files else "failed",
        "existing_files": existing_files,
        "missing_files": missing_files,
        "total_files": len(required_files),
        "existing_count": len(existing_files)
    }

def validate_data_requirements() -> Dict[str, Any]:
    """Validate data requirements."""
    print("🔍 Validating data requirements...")
    
    data_dir = "data"
    if not os.path.exists(data_dir):
        return {
            "status": "failed",
            "error": "Data directory does not exist",
            "recommendation": "Create data/ directory and add CSV files"
        }
    
    csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
    
    if not csv_files:
        return {
            "status": "warning",
            "error": "No CSV files found in data directory",
            "recommendation": "Add cryptocurrency data CSV files to data/ directory"
        }
    
    # Validate CSV structure for the first file
    sample_file = os.path.join(data_dir, csv_files[0])
    try:
        df = pd.read_csv(sample_file, nrows=5)
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            return {
                "status": "failed",
                "error": f"Missing required columns: {missing_columns}",
                "found_columns": list(df.columns),
                "required_columns": required_columns
            }
        
        return {
            "status": "passed",
            "csv_files": csv_files,
            "sample_file": sample_file,
            "columns": list(df.columns),
            "sample_rows": len(df)
        }
        
    except Exception as e:
        return {
            "status": "failed",
            "error": f"Error reading CSV file: {str(e)}",
            "file": sample_file
        }

def validate_technical_indicators() -> Dict[str, Any]:
    """Validate technical indicators implementation."""
    print("🔍 Validating technical indicators...")
    
    try:
        from app.utils.indicators import calculate_technical_indicators
        
        # Test with sample data
        sample_data = {
            'open': [100, 101, 102, 103, 104] * 20,
            'high': [105, 106, 107, 108, 109] * 20,
            'low': [95, 96, 97, 98, 99] * 20,
            'close': [102, 103, 104, 105, 106] * 20,
            'volume': [1000, 1100, 1200, 1300, 1400] * 20
        }
        
        df = pd.DataFrame(sample_data)
        
        indicators = ['RSI', 'MACD', 'ATR', 'BB', 'STOCH', 'ADX', 'WillR', 'CCI', 'ROC', 'SMA', 'EMA', 'MFI', 'OBV']
        
        result_df = calculate_technical_indicators(df, 'close', indicators)
        
        # Check if indicators were added
        indicator_columns = [col for col in result_df.columns if col not in df.columns]
        
        return {
            "status": "passed",
            "indicators_requested": indicators,
            "indicators_added": len(indicator_columns),
            "new_columns": indicator_columns[:10],  # Show first 10
            "total_features": len(indicator_columns)
        }
        
    except Exception as e:
        return {
            "status": "failed",
            "error": str(e)
        }

def validate_cnn_architecture() -> Dict[str, Any]:
    """Validate CNN architecture."""
    print("🔍 Validating CNN architecture...")
    
    try:
        from app.utils.model_builder import create_cnn_model
        import numpy as np
        
        # Test model creation
        sample_x = np.random.random((100, 6, 6, 3))
        sample_labels = np.random.randint(0, 2, (100, 2))
        
        params = {
            'batch_size': 32,
            'epochs': 1,
            'lr': 0.001,
            'optimizer': 'adam',
            'conv2d_layers': {
                'filters_1': 20, 'kernel_size_1': 2, 'conv2d_drop_out_1': 0.22,
                'filters_2': 40, 'kernel_size_2': 2, 'conv2_drop_out_2': 0.05
            },
            'dense_layers': {
                'dense_nodes_1': 32, 'dense_drop_out_1': 0.22
            }
        }
        
        model = create_cnn_model(params, sample_x, sample_labels, multiclass=False)
        
        return {
            "status": "passed",
            "input_shape": model.input_shape,
            "output_shape": model.output_shape,
            "total_params": model.count_params(),
            "trainable_params": sum([np.prod(v.get_shape()) for v in model.trainable_weights]),
            "layers": len(model.layers)
        }
        
    except Exception as e:
        return {
            "status": "failed",
            "error": str(e)
        }

def validate_optuna_integration() -> Dict[str, Any]:
    """Validate Optuna integration."""
    print("🔍 Validating Optuna integration...")
    
    try:
        from app.utils.optuna_optimizer import OptunaHyperparameterOptimizer
        
        # Test optimizer creation
        optimizer = OptunaHyperparameterOptimizer()
        
        # Test parameter suggestion (without actual optimization)
        default_params = optimizer.get_best_params()
        
        return {
            "status": "passed",
            "optimizer_created": True,
            "default_params": default_params,
            "has_convert_method": hasattr(optimizer, 'convert_to_model_params')
        }
        
    except Exception as e:
        return {
            "status": "failed",
            "error": str(e)
        }

def validate_api_structure() -> Dict[str, Any]:
    """Validate API structure."""
    print("🔍 Validating API structure...")
    
    try:
        from app.api.main import app
        
        # Get all routes
        routes = []
        for route in app.routes:
            if hasattr(route, 'methods') and hasattr(route, 'path'):
                routes.append({
                    "path": route.path,
                    "methods": list(route.methods)
                })
        
        expected_routes = [
            "/health",
            "/datasets", 
            "/train",
            "/training/status",
            "/predict",
            "/backtest",
            "/models",
            "/models/cleanup",
            "/models/cleanup/status",
            "/optimize"
        ]
        
        found_routes = [route["path"] for route in routes]
        missing_routes = [route for route in expected_routes if route not in found_routes]
        
        return {
            "status": "passed" if not missing_routes else "warning",
            "total_routes": len(routes),
            "expected_routes": expected_routes,
            "found_routes": found_routes,
            "missing_routes": missing_routes
        }
        
    except Exception as e:
        return {
            "status": "failed",
            "error": str(e)
        }

def validate_docker_configuration() -> Dict[str, Any]:
    """Validate Docker configuration."""
    print("🔍 Validating Docker configuration...")
    
    docker_files = {
        "Dockerfile": os.path.exists("Dockerfile"),
        "docker-compose.yml": os.path.exists("docker-compose.yml"),
        "docker-compose.arm64.yml": os.path.exists("docker-compose.arm64.yml"),
        "requirements.txt": os.path.exists("requirements.txt")
    }
    
    missing_files = [file for file, exists in docker_files.items() if not exists]
    
    # Check requirements.txt content
    requirements_content = []
    if docker_files["requirements.txt"]:
        try:
            with open("requirements.txt", "r") as f:
                requirements_content = [line.strip() for line in f.readlines() if line.strip() and not line.startswith("#")]
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Error reading requirements.txt: {str(e)}"
            }
    
    return {
        "status": "passed" if not missing_files else "failed",
        "docker_files": docker_files,
        "missing_files": missing_files,
        "requirements_count": len(requirements_content),
        "key_requirements": [req for req in requirements_content if any(key in req.lower() for key in ['tensorflow', 'fastapi', 'optuna', 'pandas'])]
    }

def main():
    """Run complete project validation."""
    print("🚀 Starting complete project validation")
    print("=" * 60)
    
    validations = {
        "Project Structure": validate_project_structure,
        "Data Requirements": validate_data_requirements,
        "Technical Indicators": validate_technical_indicators,
        "CNN Architecture": validate_cnn_architecture,
        "Optuna Integration": validate_optuna_integration,
        "API Structure": validate_api_structure,
        "Docker Configuration": validate_docker_configuration
    }
    
    results = {}
    
    for validation_name, validation_func in validations.items():
        try:
            result = validation_func()
            results[validation_name] = result
            
            status = result["status"]
            if status == "passed":
                print(f"✅ {validation_name}: PASSED")
            elif status == "warning":
                print(f"⚠️ {validation_name}: WARNING")
            else:
                print(f"❌ {validation_name}: FAILED")
                
            if "error" in result:
                print(f"   Error: {result['error']}")
                
        except Exception as e:
            print(f"❌ {validation_name}: ERROR - {str(e)}")
            results[validation_name] = {"status": "error", "error": str(e)}
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 VALIDATION SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for r in results.values() if r["status"] == "passed")
    warnings = sum(1 for r in results.values() if r["status"] == "warning")
    failed = sum(1 for r in results.values() if r["status"] in ["failed", "error"])
    total = len(results)
    
    print(f"✅ PASSED:   {passed}/{total}")
    print(f"⚠️ WARNINGS: {warnings}/{total}")
    print(f"❌ FAILED:   {failed}/{total}")
    
    if failed == 0:
        print("\n🎉 Project validation successful! Ready for production deployment.")
        return True
    else:
        print("\n⚠️ Project has issues that need to be addressed before production deployment.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
