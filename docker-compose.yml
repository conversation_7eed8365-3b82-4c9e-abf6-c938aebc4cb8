version: '3.8'

services:
  trend-prediction-api:
    build: .
    image: trend-prediction-service
    container_name: trend-prediction-service
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./models:/app/models
    environment:
      - TF_CPP_MIN_LOG_LEVEL=2
      - MODEL_DIR=/app/models
      - DATA_DIR=/app/data
      - LOG_LEVEL=INFO
      - MODEL_MAX_AGE_DAYS=90
      - MODEL_MAX_VERSIONS=5
      - MODEL_CLEANUP_ENABLED=true
      - MAX_CONCURRENT_TRAININGS=2
      - SUPPORTED_PAIRS=BTCUSDT,ETHUSDT
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/docs"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
