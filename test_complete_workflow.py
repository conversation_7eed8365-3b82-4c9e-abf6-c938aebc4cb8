#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Complete workflow test for the Trend Prediction Service.
This script tests all major functionalities of the service.
"""

import requests
import time
import json
import os
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
TEST_PAIR = "BTCUSDT"
TEST_DATA_PATH = f"data/{TEST_PAIR}_1h.csv"

def test_health_check() -> bool:
    """Test the health check endpoint."""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {str(e)}")
        return False

def test_datasets_endpoint() -> bool:
    """Test the datasets endpoint."""
    print("🔍 Testing datasets endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/datasets")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Datasets endpoint passed: {len(data.get('datasets', []))} datasets found")
            return True
        else:
            print(f"❌ Datasets endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Datasets endpoint error: {str(e)}")
        return False

def test_training_with_optimization() -> Dict[str, Any]:
    """Test training with automatic optimization."""
    print("🔍 Testing training with automatic optimization...")
    try:
        # Check if data file exists
        if not os.path.exists(TEST_DATA_PATH):
            print(f"❌ Test data file not found: {TEST_DATA_PATH}")
            return {"success": False, "error": "Data file not found"}

        # Start training with optimization
        training_request = {
            "pair": TEST_PAIR,
            "epochs": 10,  # Reduced for testing
            "batch_size": 40,
            "learning_rate": 0.001,
            "auto_optimize": True,
            "optimization_trials": 3,  # Reduced for testing
            "optimization_timeout": 300  # 5 minutes for testing
        }

        print(f"📤 Starting training with request: {training_request}")
        response = requests.post(f"{BASE_URL}/train", json=training_request)
        
        if response.status_code == 200:
            data = response.json()
            training_id = data.get("training_id")
            print(f"✅ Training started successfully: {training_id}")
            return {"success": True, "training_id": training_id, "data": data}
        else:
            print(f"❌ Training failed: {response.status_code} - {response.text}")
            return {"success": False, "error": response.text}
    except Exception as e:
        print(f"❌ Training error: {str(e)}")
        return {"success": False, "error": str(e)}

def monitor_training(max_wait_minutes: int = 15) -> bool:
    """Monitor training progress."""
    print("🔍 Monitoring training progress...")
    start_time = time.time()
    max_wait_seconds = max_wait_minutes * 60

    while time.time() - start_time < max_wait_seconds:
        try:
            response = requests.get(f"{BASE_URL}/training/status", params={"pair": TEST_PAIR})
            
            if response.status_code == 200:
                data = response.json()
                status = data.get("status", "unknown")
                progress = data.get("progress", 0)
                current_epoch = data.get("current_epoch", 0)
                total_epochs = data.get("total_epochs", 0)
                elapsed_time = data.get("elapsed_time", 0)
                optimization = data.get("optimization", {})
                system_monitoring = data.get("system_monitoring", {})

                print(f"📊 Status: {status}, Progress: {progress:.1f}%, "
                      f"Epoch: {current_epoch}/{total_epochs}, "
                      f"Elapsed: {elapsed_time:.1f}s")

                if optimization:
                    print(f"🔧 Optimization: {optimization}")

                if system_monitoring:
                    cpu = system_monitoring.get("cpu", {})
                    memory = system_monitoring.get("memory", {})
                    print(f"💻 System: CPU {cpu.get('usage_percent', 0):.1f}%, "
                          f"Memory {memory.get('usage_percent', 0):.1f}%")

                if status == "completed":
                    print("✅ Training completed successfully!")
                    return True
                elif status == "failed":
                    error = data.get("error", "Unknown error")
                    print(f"❌ Training failed: {error}")
                    return False

            time.sleep(30)  # Wait 30 seconds before next check

        except Exception as e:
            print(f"⚠️ Error monitoring training: {str(e)}")
            time.sleep(30)

    print(f"⏰ Training monitoring timeout after {max_wait_minutes} minutes")
    return False

def test_models_endpoint() -> bool:
    """Test the models endpoint."""
    print("🔍 Testing models endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/models")
        if response.status_code == 200:
            data = response.json()
            models = data.get("models", [])
            print(f"✅ Models endpoint passed: {len(models)} models found")
            
            # Print model details
            for model in models:
                print(f"  📁 Model: {model.get('pair')} - {model.get('performance', {})}")
            
            return True
        else:
            print(f"❌ Models endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Models endpoint error: {str(e)}")
        return False

def test_prediction() -> bool:
    """Test prediction functionality."""
    print("🔍 Testing prediction...")
    try:
        # Sample market data for prediction
        market_data = [
            {
                "timestamp": 1625097600000,
                "open": 35000.0,
                "high": 36000.0,
                "low": 34500.0,
                "close": 35500.0,
                "volume": 1000.0,
                "symbol": TEST_PAIR
            }
        ]

        prediction_request = {"market_data": market_data}
        
        response = requests.post(f"{BASE_URL}/predict", json=prediction_request)
        
        if response.status_code == 200:
            data = response.json()
            prediction = data.get("prediction")
            confidence = data.get("confidence")
            print(f"✅ Prediction successful: {prediction} (confidence: {confidence:.4f})")
            return True
        else:
            print(f"❌ Prediction failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Prediction error: {str(e)}")
        return False

def test_backtest() -> bool:
    """Test backtesting functionality."""
    print("🔍 Testing backtesting...")
    try:
        backtest_request = {
            "model_path": f"models/{TEST_PAIR}/{TEST_PAIR}_best_model.h5",
            "data_path": TEST_DATA_PATH,
            "window": 24,
            "generate_plots": False
        }

        response = requests.post(f"{BASE_URL}/backtest", json=backtest_request)
        
        if response.status_code == 200:
            data = response.json()
            metrics = data.get("metrics", {})
            print(f"✅ Backtest successful: {metrics}")
            return True
        else:
            print(f"❌ Backtest failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Backtest error: {str(e)}")
        return False

def test_cleanup() -> bool:
    """Test model cleanup functionality."""
    print("🔍 Testing model cleanup...")
    try:
        # First check cleanup status
        response = requests.get(f"{BASE_URL}/models/cleanup/status")
        if response.status_code == 200:
            status_data = response.json()
            print(f"📊 Cleanup status: {status_data}")

        # Run cleanup
        response = requests.post(f"{BASE_URL}/models/cleanup")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Cleanup successful: {data}")
            return True
        else:
            print(f"❌ Cleanup failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Cleanup error: {str(e)}")
        return False

def main():
    """Run complete workflow test."""
    print("🚀 Starting complete workflow test for Trend Prediction Service")
    print("=" * 60)

    # Test results
    results = {}

    # 1. Health check
    results["health"] = test_health_check()
    
    # 2. Datasets
    results["datasets"] = test_datasets_endpoint()
    
    # 3. Training with optimization
    training_result = test_training_with_optimization()
    results["training"] = training_result["success"]
    
    if results["training"]:
        # 4. Monitor training
        results["monitoring"] = monitor_training(max_wait_minutes=15)
        
        if results["monitoring"]:
            # 5. Test models endpoint
            results["models"] = test_models_endpoint()
            
            # 6. Test prediction
            results["prediction"] = test_prediction()
            
            # 7. Test backtesting
            results["backtest"] = test_backtest()
    
    # 8. Test cleanup
    results["cleanup"] = test_cleanup()

    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name.upper():<15}: {status}")
    
    print(f"\nOVERALL: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! The service is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the logs for details.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
