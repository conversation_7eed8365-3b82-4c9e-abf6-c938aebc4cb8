# Trend Prediction Service

A production-ready cryptocurrency trend prediction service using Convolutional Neural Networks (CNN) for 24-hour price movement forecasting. The service provides REST API endpoints for training models, making predictions, and backtesting performance.

## 🚀 Features

- **24-Hour Trend Prediction**: Predicts cryptocurrency price movement 24 hours ahead
- **Real-time API**: FastAPI-based REST API for predictions and model management
- **Automated Training**: Background model training with progress monitoring
- **Performance Backtesting**: Comprehensive model evaluation with temporal validation
- **Technical Indicators**: 13+ technical indicators for feature engineering
- **Containerized Deployment**: Docker-ready for easy deployment
- **Production Ready**: Health checks, logging, and error handling

## 📊 Model Architecture

- **Input**: Technical indicators transformed to 6x6x3 image format
- **Architecture**: CNN with 2 convolutional layers + 2 dense layers
- **Parameters**: 24,078 trainable parameters
- **Output**: Binary classification (up/down price movement)
- **Training**: 64 epochs with early stopping and learning rate reduction

## 🛠 Technical Stack

- **Framework**: FastAPI + Uvicorn
- **ML Library**: TensorFlow/Keras
- **Data Processing**: Pandas, NumPy, scikit-learn
- **Technical Analysis**: TA library
- **Visualization**: Matplotlib
- **Containerization**: Docker

## 📁 Project Structure

```
├── app/
│   ├── api/
│   │   └── main.py              # Main API endpoints
│   ├── models/
│   │   ├── trainer.py           # Model training logic
│   │   ├── predictor.py         # Prediction logic
│   │   └── backtester.py        # Backtesting functionality
│   └── utils/
│       ├── config.py            # Configuration settings
│       ├── data_processor.py    # Data preprocessing
│       ├── indicators.py        # Technical indicators
│       ├── model_builder.py     # CNN model architecture
│       └── training_callbacks.py # Training callbacks
├── data/
│   └── BTCUSDT_1h.csv          # Historical BTCUSDT data
├── models/
│   └── BTCUSDT/                # Trained models directory
├── Dockerfile                   # Docker configuration
├── docker-compose.yml          # Docker Compose setup
├── requirements.txt            # Python dependencies
└── README.md                   # This file
```

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- 4GB+ RAM (for TensorFlow)
- 2GB+ disk space

### 1. Clone and Build

```bash
git clone <repository-url>
cd predictmarket-TrendPredictionService
docker-compose up --build
```

### 2. Verify Health

```bash
curl http://localhost:8000/health
```

### 3. Train Your First Model

```bash
curl -X POST "http://localhost:8000/train" \
  -H "Content-Type: application/json" \
  -d '{
    "data_path": "data/BTCUSDT_1h.csv",
    "epochs": 64,
    "batch_size": 80,
    "learning_rate": 0.001,
    "pair": "BTCUSDT"
  }'
```

### 4. Monitor Training Progress

```bash
curl "http://localhost:8000/training/status?pair=BTCUSDT"
```

### 5. Make Predictions

```bash
curl -X POST "http://localhost:8000/predict" \
  -H "Content-Type: application/json" \
  -d '{
    "market_data": [
      {
        "timestamp": 1625097600000,
        "open": 35000.0,
        "high": 36000.0,
        "low": 34500.0,
        "close": 35500.0,
        "volume": 1000.0,
        "symbol": "BTCUSDT"
      }
    ]
  }'
```

### 6. Backtest Model Performance

```bash
curl -X POST "http://localhost:8000/backtest" \
  -H "Content-Type: application/json" \
  -d '{
    "model_path": "models/BTCUSDT/best_model.h5",
    "data_path": "data/BTCUSDT_1h.csv",
    "window": 24,
    "generate_plots": false
  }'
```

## 📈 Expected Performance

Based on real BTCUSDT data testing:

- **Training Accuracy**: ~50% (expected for 24h prediction)
- **Backtesting Accuracy**: ~81%
- **F1 Score**: ~78%
- **Precision**: ~79%
- **Recall**: ~74%

## 🔧 Configuration

### Environment Variables

```bash
# Model settings
MODEL_DIR=/app/models
DATA_DIR=/app/data
DEFAULT_PAIR=BTCUSDT
SUPPORTED_PAIRS=BTCUSDT

# Training settings
MAX_CONCURRENT_TRAININGS=2

# Logging
LOG_LEVEL=INFO
TF_CPP_MIN_LOG_LEVEL=2
```

### Training Parameters

- **Epochs**: 64 (with early stopping)
- **Batch Size**: 80
- **Learning Rate**: 0.001
- **Optimizer**: Adam
- **Early Stopping Patience**: 50 epochs
- **Prediction Window**: 24 hours

## 📊 Technical Indicators

The model uses 13 technical indicators:

1. **RSI** - Relative Strength Index
2. **MACD** - Moving Average Convergence Divergence
3. **ATR** - Average True Range
4. **BB** - Bollinger Bands
5. **STOCH** - Stochastic Oscillator
6. **ADX** - Average Directional Index
7. **WillR** - Williams %R
8. **CCI** - Commodity Channel Index
9. **ROC** - Rate of Change
10. **SMA** - Simple Moving Average
11. **EMA** - Exponential Moving Average
12. **MFI** - Money Flow Index
13. **OBV** - On-Balance Volume

## 🧪 Testing from Scratch

To test the complete system:

1. **Clean Environment**: Remove all pre-trained models
2. **Fresh Training**: Train on full dataset
3. **Performance Validation**: Backtest with temporal split
4. **API Testing**: Test all endpoints
5. **Production Readiness**: Verify health checks and logging

```bash
# Clean models
docker exec trend-prediction-service rm -rf /app/models/BTCUSDT/*

# Start fresh training
curl -X POST "http://localhost:8000/train" \
  -H "Content-Type: application/json" \
  -d '{"data_path": "data/BTCUSDT_1h.csv", "epochs": 64, "pair": "BTCUSDT"}'

# Monitor and test
curl "http://localhost:8000/training/status?pair=BTCUSDT"
curl "http://localhost:8000/models"
```

## 🐳 Docker Deployment

### Production Deployment

```bash
# Build production image
docker build -t trend-prediction-service .

# Run with resource limits
docker run -d \
  --name trend-prediction \
  --memory=4g \
  --cpus=2 \
  -p 8000:8000 \
  trend-prediction-service
```

### ARM64 Support (Apple Silicon)

```bash
docker-compose -f docker-compose.arm64.yml up --build
```

## 📝 API Documentation

Once running, visit:
- **Interactive API Docs**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## 🔍 Monitoring and Logging

- **Health Checks**: Built-in Docker health checks
- **Structured Logging**: JSON-formatted logs with timestamps
- **Training Progress**: Real-time training status monitoring
- **Performance Metrics**: Comprehensive backtesting results

## 🚨 Troubleshooting

### Common Issues

1. **Memory Issues**: Ensure 4GB+ RAM available
2. **Training Fails**: Check data file exists and format
3. **Prediction Errors**: Verify model is trained first
4. **Port Conflicts**: Change port mapping in docker-compose.yml

### Debug Mode

```bash
# View logs
docker logs trend-prediction-service

# Access container
docker exec -it trend-prediction-service bash

# Check model files
docker exec trend-prediction-service ls -la /app/models/BTCUSDT/
```

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For issues and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review API documentation at `/docs`
