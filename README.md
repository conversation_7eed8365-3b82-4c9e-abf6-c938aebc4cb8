# Trend Prediction Service

A containerized API service for predicting cryptocurrency market trends using a CNN model.

## Overview

This service provides an API for predicting market trends based on technical indicators. It uses a Convolutional Neural Network (CNN) to transform financial data into image-like representations and leverage the pattern recognition capabilities of CNNs.

The service is designed to be autonomous in model deployment, supporting multiple cryptocurrency pairs and automatically selecting the best-performing model based on backtesting results.

## Features

- **Prediction API**: Submit market data and get trend predictions for any supported cryptocurrency pair
- **Training API**: Retrain models with new data for any cryptocurrency pair
- **Model Management**: Automatic comparison and selection of the best-performing model
- **Performance Monitoring**: Backtest models and track performance metrics
- **Training Progress Monitoring**: Real-time updates on model training status
- **Automatic Model Cleanup**: Background service to remove old and unused models
- **Docker Deployment**: Easy containerization for deployment

## Project Structure

```
├── app/                        # Application code
│   ├── api/                    # API endpoints
│   │   ├── main.py             # Main API entry point
│   │   ├── training.py         # Training progress monitoring
│   ├── models/                 # Model implementations
│   │   ├── backtester.py       # Backtesting functionality
│   │   ├── predictor.py        # Prediction functionality
│   │   ├── trainer.py          # Training functionality
│   ├── utils/                  # Utility modules
│   │   ├── config.py           # Configuration settings
│   │   ├── data_processor.py   # Data processing
│   │   ├── indicators.py       # Technical indicators
│   │   ├── model_builder.py    # Model creation
│   │   ├── model_cleanup.py    # Model cleanup service
│   │   ├── training_callbacks.py # Training callbacks
│   ├── tests/                  # Unit tests
├── data/                       # Market data
│   ├── BTCUSDT_1h.csv          # Sample data
├── models/                     # Trained models
├── Dockerfile                  # Docker configuration
├── docker-compose.yml          # Docker Compose configuration
├── requirements.txt            # Python dependencies
```

## Installation

### Using Docker (Recommended)

1. Make sure Docker and Docker Compose are installed on your system.

2. Build and run the Docker container:
   ```bash
   docker-compose up -d
   ```

### Local Installation

1. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Run the API:
   ```bash
   uvicorn app.api.main:app --host 0.0.0.0 --port 8000 --reload
   ```

## API Usage

Once the service is running, you can access the API documentation at:
```
http://localhost:8000/docs
```

### Prediction Endpoint

```bash
curl -X POST "http://localhost:8000/predict" \
  -H "Content-Type: application/json" \
  -d '{
    "market_data": [
      {
        "timestamp": 1609459200000,
        "open": 29000.0,
        "high": 29300.0,
        "low": 28800.0,
        "close": 29100.0,
        "volume": 1000.0,
        "num_trades": 100,
        "symbol": "BTCUSDT"
      },
      ...
    ]
  }'
```

### Training Endpoint

```bash
curl -X POST "http://localhost:8000/train" \
  -H "Content-Type: application/json" \
  -d '{
    "data_path": "data/BTCUSDT_1h.csv",
    "epochs": 64,
    "batch_size": 80,
    "learning_rate": 0.001,
    "pair": "BTCUSDT"
  }'
```

### Backtesting Endpoint

```bash
curl -X POST "http://localhost:8000/backtest" \
  -H "Content-Type: application/json" \
  -d '{
    "model_path": "models/BTCUSDT/best_model.h5",
    "data_path": "data/BTCUSDT_1h.csv",
    "window": 24,
    "generate_plots": true
  }'
```

### Model Comparison Endpoint

```bash
curl -X POST "http://localhost:8000/backtest/compare" \
  -H "Content-Type: application/json" \
  -d '{
    "model_paths": [
      "models/BTCUSDT/model_20230101_120000.h5",
      "models/BTCUSDT/model_20230201_120000.h5"
    ],
    "data_path": "data/BTCUSDT_1h.csv",
    "window": 24
  }'
```

### Get Backtesting Results

```bash
curl -X GET "http://localhost:8000/backtest/results"
```

### Training Progress Monitoring

```bash
curl -X GET "http://localhost:8000/training/status?pair=BTCUSDT"
```

## Model Details

The CNN model transforms financial indicators into image-like representations to leverage the pattern recognition capabilities of CNNs. The model architecture includes:

- Convolutional layers to extract features
- Dropout layers to prevent overfitting
- Dense layers for classification
- The model is trained to predict whether the price will go up or down in the future

## Technical Indicators

The service calculates various technical indicators to enrich market data:
- RSI (Relative Strength Index)
- MACD (Moving Average Convergence Divergence)
- Bollinger Bands
- Stochastic Oscillator
- ADX (Average Directional Index)
- Williams %R
- CCI (Commodity Channel Index)
- ROC (Rate of Change)
- Moving Averages (SMA, EMA)
- MFI (Money Flow Index)
- OBV (On-Balance Volume)

## Performance Monitoring

The service includes comprehensive performance monitoring capabilities:

### Backtesting

The backtesting functionality allows you to evaluate model performance on historical data. It calculates various metrics including:

- **Accuracy**: Percentage of correct predictions
- **Precision**: Ratio of true positives to all positive predictions
- **Recall**: Ratio of true positives to all actual positives
- **F1 Score**: Harmonic mean of precision and recall
- **AUC**: Area under the ROC curve
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Ratio of gross profit to gross loss
- **Maximum Drawdown**: Largest drop from peak to trough
- **Sharpe Ratio**: Risk-adjusted return

### Visualization

The backtesting results include visualizations to help understand model performance:

- **Price Chart with Predictions**: Shows buy/sell signals on the price chart
- **ROC Curve**: Visualizes the trade-off between true positive rate and false positive rate
- **Confusion Matrix**: Shows the distribution of true/false positives and negatives
- **Cumulative Returns**: Displays the cumulative returns over time

### Model Comparison

The model comparison functionality allows you to compare multiple models on the same dataset. It identifies the best model based on different metrics such as accuracy, F1 score, AUC, profit factor, and Sharpe ratio.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
