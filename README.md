# Cryptocurrency Trend Prediction Service

**License: Private & Exclusive**

A production-ready containerized machine learning service for cryptocurrency trend prediction using advanced Convolutional Neural Networks with technical indicator image transformation and Bayesian hyperparameter optimization.

This system transforms financial time series data into image representations for CNN-based pattern recognition, providing enterprise-grade cryptocurrency trend forecasting through a comprehensive RESTful API with automated model optimization and deployment capabilities.

## 🎯 Technical Overview & Methodology

### Core Innovation: Technical Indicators to Image Transformation

The service implements a unique methodology that converts traditional financial technical indicators into spatial image data for CNN processing:

1. **Technical Indicator Calculation**: 30+ indicators (RSI, MACD, Bollinger Bands, etc.)
2. **Normalization**: Scale all indicators to [0, 1] range
3. **Image Transformation**: Reshape indicators into 6x6x3 tensor format
4. **Spatial Pattern Recognition**: CNN processes spatial relationships between indicators
5. **Temporal Labeling**: 24-hour trend prediction using moving average methodology

### Machine Learning Pipeline

```
Raw OHLCV Data → Technical Indicators → Image Transformation → CNN Model → Trend Prediction
      ↓                    ↓                      ↓               ↓              ↓
   Hourly Data      RSI, MACD, BB, etc.    6x6x3 Tensors    Pattern Recognition  Binary Classification
```

### Advanced CNN Architecture

**Input Processing:**
- **Data Format**: OHLCV (Open, High, Low, Close, Volume) hourly data
- **Technical Indicators**: 30+ indicators across momentum, trend, volatility, volume categories
- **Image Transformation**: Reshape indicators into 6x6x3 matrices for spatial pattern recognition
- **Temporal Labeling**: 24-hour trend prediction using moving average methodology

**Network Design (Optimized):**
- **Layer 1**: Conv2D with optimized filters (8-48), kernel size (2-4), dropout (0.02-0.4)
- **Layer 2**: Conv2D with optimized filters (16-96), kernel size (2-4), dropout (0.01-0.3)
- **Layer 3**: Conv2D with optimized filters (32-128), kernel size (2-3), dropout (0.05-0.35)
- **Dense 1**: Configurable nodes (12-96), dropout (0.1-0.6)
- **Dense 2**: Configurable nodes (8-64), dropout (0.05-0.4)
- **Output**: Binary classification (trend up/down) with softmax activation
- **Optimization**: AdamW/Adam/RMSprop with configurable parameters

### Bayesian Hyperparameter Optimization (Optuna)

**Advanced Search Space:**
- **Training Parameters**: epochs (32-128), batch_size (24-140), learning_rate (0.0001-0.02)
- **Architecture**: 3 conv layers + 2 dense layers with extensive parameter ranges
- **Regularization**: 5 dropout layers, weight decay, batch normalization
- **Optimization**: AdamW/Adam/RMSprop with beta parameters
- **Advanced Options**: kernel sizes, pool sizes, strides, activation functions

**Optimization Process:**
1. **Tree-structured Parzen Estimator (TPE)** for intelligent parameter search
2. **MedianPruner** for early stopping of unpromising trials
3. **Memory Management** with automatic cleanup between trials
4. **Reduced epochs per trial** (15) for faster optimization cycles
5. **Comprehensive logging** and result persistence

## 🏗️ System Architecture

### Technical Stack

- **Backend Framework**: FastAPI with async/await for high-performance API serving
- **Machine Learning**: TensorFlow/Keras with custom CNN architectures
- **Optimization**: Optuna for Bayesian hyperparameter optimization with pruning
- **Data Processing**: Pandas, NumPy, TA-Lib for technical analysis
- **Containerization**: Docker with multi-stage builds and ARM64/x86_64 support
- **API Documentation**: Automatic OpenAPI/Swagger documentation generation
- **Monitoring**: Built-in system resource monitoring and performance tracking

### Data Processing Methodology

**Technical Indicator Calculation:**

**Momentum Indicators:**
- RSI (Relative Strength Index) with multiple periods
- Stochastic Oscillator (%K, %D)
- Williams %R
- Rate of Change (ROC)

**Trend Indicators:**
- MACD (Moving Average Convergence Divergence)
- ADX (Average Directional Index)
- CCI (Commodity Channel Index)

**Volatility Indicators:**
- ATR (Average True Range)
- Bollinger Bands (Upper, Middle, Lower)

**Volume Indicators:**
- MFI (Money Flow Index)
- OBV (On-Balance Volume)

**Moving Averages:**
- Simple Moving Averages (SMA) - multiple periods
- Exponential Moving Averages (EMA) - multiple periods

### Backtesting Framework with Prediction Analysis

**Primary Focus: Prediction Accuracy Analysis**
- **Total Predictions Tracking**: Count of all predictions made
- **Correct/Incorrect Breakdown**: Detailed analysis of prediction accuracy
- **Type-Specific Accuracy**: Separate accuracy rates for UP and DOWN predictions
- **False Positive/Negative Analysis**: Detailed error analysis
- **Confusion Matrix**: Complete classification performance matrix

**Secondary: Ultra Simple Trading Strategy**
- **Strategy**: Buy on UP prediction, Hold cash on DOWN prediction
- **Position Sizing**: 2% of capital per trade (realistic)
- **Transaction Costs**: 0.1% per trade (realistic)
- **Risk Management**: Maximum drawdown tracking
- **Performance Metrics**: Sharpe ratio (capped at 5.0), profit factor, total return

## 🚀 Complete Deployment Guide

### Prerequisites

**System Requirements:**
- Docker and Docker Compose installed
- 4GB+ RAM (8GB+ recommended for optimization)
- 5GB+ free disk space
- ARM64 (Apple Silicon) or x86_64 architecture

**Supported Platforms:**
- macOS (ARM64/Intel)
- Linux (ARM64/x86_64)
- Windows with WSL2

### Step-by-Step Installation

#### 1. Repository Setup

```bash
# Clone the repository
git clone <repository-url>
cd predictmarket-TrendPredictionService

# Verify directory structure
ls -la
# Expected: app/ data/ docker-compose*.yml Dockerfile* requirements.txt README.md
```

#### 2. Data Preparation

```bash
# Verify data files
ls -la data/
# Expected: BTCUSDT_1h.csv (or your cryptocurrency pair data)

# Data format requirements:
# - CSV file with columns: timestamp, open, high, low, close, volume
# - Hourly data recommended
# - Minimum 10,000 rows for effective training
# - File naming: {PAIR}_1h.csv (e.g., BTCUSDT_1h.csv, ETHUSDT_1h.csv)
```

#### 3. Service Deployment

**For ARM64 (Apple Silicon):**
```bash
docker-compose -f docker-compose.arm64.yml up --build -d
```

**For x86_64 (Intel/AMD):**
```bash
docker-compose up --build -d
```

**Verify deployment:**
```bash
# Check service status
docker ps
# Expected: trend-prediction-service container running

# Test health endpoint
curl http://localhost:8000/health
# Expected: {"status":"ok","timestamp":"..."}
```

#### 4. Service Verification

```bash
# Access API documentation
open http://localhost:8000/docs

# Check available datasets
curl http://localhost:8000/datasets | jq .

# Check system status
curl http://localhost:8000/models | jq .
```

## 🔧 Complete API Usage Guide

### Core Endpoints

#### 1. Health Check
```bash
curl http://localhost:8000/health
```
**Response:**
```json
{
  "status": "ok",
  "timestamp": "2025-05-30T11:36:54.391652"
}
```

#### 2. Dataset Management
```bash
curl http://localhost:8000/datasets
```
**Response:**
```json
{
  "datasets": [
    {
      "pair": "BTCUSDT",
      "filename": "BTCUSDT_1h.csv",
      "file_path": "data/BTCUSDT_1h.csv",
      "size_bytes": 3740693,
      "size_mb": 3.57,
      "last_modified": "2025-04-09T13:38:02"
    }
  ]
}
```

#### 3. Model Training with Automatic Optimization

**Basic Training:**
```bash
curl -X POST http://localhost:8000/train \
  -H "Content-Type: application/json" \
  -d '{
    "pair": "BTCUSDT",
    "epochs": 64,
    "batch_size": 80,
    "learning_rate": 0.001
  }'
```

**Advanced Training with Optimization:**
```bash
curl -X POST http://localhost:8000/train \
  -H "Content-Type: application/json" \
  -d '{
    "pair": "BTCUSDT",
    "epochs": 96,
    "batch_size": 80,
    "learning_rate": 0.001,
    "auto_optimize": true,
    "optimization_trials": 20,
    "optimization_timeout": 1000
  }'
```

**Parameters:**
- `pair`: Cryptocurrency pair (must match data file)
- `epochs`: Training epochs (32-128 recommended)
- `batch_size`: Batch size (24-140, powers of 2 recommended)
- `learning_rate`: Initial learning rate (0.0001-0.02)
- `auto_optimize`: Enable Optuna optimization (default: true)
- `optimization_trials`: Number of optimization trials (8-20 recommended)
- `optimization_timeout`: Timeout in seconds (600-1200 recommended)

#### 4. Training Progress Monitoring

```bash
curl "http://localhost:8000/training/status?pair=BTCUSDT"
```

**Response:**
```json
{
  "training_id": "training-1748604525",
  "status": "completed",
  "progress": 1.0,
  "metrics": {
    "accuracy": 0.6646149981158146,
    "f1_score": 0.6715867158671587,
    "val_loss": 0.5415799021720886
  },
  "current_epoch": 96,
  "total_epochs": 96,
  "elapsed_time": 390.02174282073975,
  "optimization": {
    "best_f1_score": 0.7156,
    "best_params": {...}
  },
  "system_monitoring": {
    "cpu": {"usage_percent": 0.0},
    "memory": {"usage_percent": 18.8},
    "disk": {"usage_percent": 4.63}
  }
}
```

#### 5. Model Inference

```bash
curl -X POST http://localhost:8000/predict \
  -H "Content-Type: application/json" \
  -d '{
    "market_data": [
      {
        "timestamp": 1625097600000,
        "open": 35000.0,
        "high": 36000.0,
        "low": 34500.0,
        "close": 35500.0,
        "volume": 1000.0,
        "symbol": "BTCUSDT"
      }
    ]
  }'
```

**Response:**
```json
{
  "predictions": [
    {
      "timestamp": 1625097600000,
      "symbol": "BTCUSDT",
      "prediction": 1,
      "confidence": 0.75
    }
  ]
}
```

#### 6. Backtesting with Prediction Analysis

```bash
curl -X POST http://localhost:8000/backtest \
  -H "Content-Type: application/json" \
  -d '{
    "model_path": "models/BTCUSDT/best_model.h5",
    "data_path": "data/BTCUSDT_1h.csv",
    "window": 24,
    "generate_plots": false
  }'
```

**Response:**
```json
{
  "performance": {
    "accuracy": 0.7174076865109269,
    "f1_score": 0.7254088357334635,
    "total_predictions": 3981,
    "correct_predictions": 2856,
    "incorrect_predictions": 1125,
    "prediction_accuracy_pct": 71.74,
    "up_predictions": 1933,
    "down_predictions": 2048,
    "correct_up_predictions": 1486,
    "correct_down_predictions": 1370,
    "up_prediction_accuracy_pct": 76.88,
    "down_prediction_accuracy_pct": 66.89,
    "win_rate": 0.7174076865109269,
    "profit_factor": 4.17,
    "total_return": 29.84,
    "max_drawdown": 1.12,
    "sharpe_ratio": 5.0
  }
}
```

#### 7. Model Management

**List Models:**
```bash
curl http://localhost:8000/models
```

**Model Cleanup:**
```bash
# Trigger cleanup
curl -X POST http://localhost:8000/models/cleanup

# Check cleanup status
curl http://localhost:8000/models/cleanup/status
```

## 📊 Performance Analysis & Interpretation

### Understanding Prediction Metrics

**Primary Metrics (Prediction Accuracy):**
- **Total Predictions**: Total number of predictions made
- **Correct Predictions**: Number of accurate predictions
- **Prediction Accuracy %**: Overall accuracy percentage
- **UP Prediction Accuracy**: Accuracy for upward trend predictions
- **DOWN Prediction Accuracy**: Accuracy for downward trend predictions

**Secondary Metrics (Trading Performance):**
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Ratio of gross profit to gross loss
- **Total Return**: Cumulative portfolio performance
- **Max Drawdown**: Largest peak-to-trough decline
- **Sharpe Ratio**: Risk-adjusted returns (capped at 5.0)

### Optimization Results Interpretation

**Optuna Optimization Output:**
- **Best F1 Score**: Highest F1 score achieved during optimization
- **Best Parameters**: Optimal hyperparameters found
- **Trial Count**: Number of optimization trials completed
- **Optimization Time**: Time spent on hyperparameter search

**Parameter Categories:**
- **Architecture**: Conv layers, dense layers, filters, nodes
- **Regularization**: Dropout rates, weight decay, batch normalization
- **Training**: Learning rate, batch size, epochs, optimizer type
- **Advanced**: Kernel sizes, pool sizes, strides, activation functions

## 🛠️ Advanced Configuration & Customization

### Adding New Cryptocurrency Pairs

1. **Data Preparation:**
```bash
# Add new CSV file to data directory
cp YOUR_PAIR_1h.csv data/ETHUSDT_1h.csv

# Verify data format
head -5 data/ETHUSDT_1h.csv
# Expected columns: timestamp,open,high,low,close,volume
```

2. **Train New Model:**
```bash
curl -X POST http://localhost:8000/train \
  -H "Content-Type: application/json" \
  -d '{
    "pair": "ETHUSDT",
    "epochs": 64,
    "auto_optimize": true,
    "optimization_trials": 16
  }'
```

### Extending Technical Indicators

1. **Modify Indicator Calculation:**
```python
# Edit app/utils/technical_indicators.py
def calculate_custom_indicator(df):
    # Add your custom indicator logic
    return indicator_values
```

2. **Update Image Transformation:**
```python
# Edit app/utils/data_processing.py
# Ensure total indicators = 30 for 6x6x3 tensor compatibility
```

### Custom Model Architectures

1. **Modify CNN Architecture:**
```python
# Edit app/models/cnn_model.py
# Update create_cnn_model function
```

2. **Update Hyperparameter Search:**
```python
# Edit app/utils/optuna_optimizer.py
# Modify _suggest_hyperparameters method
```

## 🔒 Security & Production Considerations

### Data Security
- No sensitive data persistence beyond model files
- Input validation and sanitization on all endpoints
- Error handling without data leakage
- Secure containerized environment

### Model Security
- Model versioning and integrity checks
- Automatic cleanup of old models
- Access control through API endpoints
- Audit logging for training operations

### Production Deployment
- Stateless API design for horizontal scaling
- Resource monitoring and alerting
- Automatic error recovery
- Health checks and service monitoring

## �� Performance Characteristics & Benchmarks

### Training Performance
- **Optimization Time**: 2-5 minutes for 16-20 trials
- **Training Time**: 3-8 minutes for 64-96 epochs
- **Memory Usage**: 1-2GB during training
- **CPU Utilization**: 15-25% during optimization

### Inference Performance
- **Prediction Latency**: <100ms per prediction
- **Throughput**: 100+ predictions/second
- **Memory Footprint**: <500MB at rest
- **Model Loading Time**: <5 seconds

### Backtesting Performance
- **Analysis Speed**: 2,000+ trades in <30 seconds
- **Memory Efficiency**: Streaming data processing
- **Scalability**: Multi-year datasets supported
- **Metrics Calculation**: Real-time

## 🚨 Troubleshooting Guide

### Common Issues

**1. Service Won't Start:**
```bash
# Check Docker status
docker ps -a

# Check logs
docker logs trend-prediction-service

# Restart service
docker-compose down && docker-compose up -d
```

**2. Training Fails:**
```bash
# Check data format
head -5 data/BTCUSDT_1h.csv

# Verify data size
wc -l data/BTCUSDT_1h.csv
# Should be >10,000 lines

# Check training logs
curl "http://localhost:8000/training/status?pair=BTCUSDT"
```

**3. Optimization Issues:**
```bash
# Reduce optimization trials
curl -X POST http://localhost:8000/train \
  -d '{"pair": "BTCUSDT", "optimization_trials": 8}'

# Check system resources
curl http://localhost:8000/training/status | jq .system_monitoring
```

**4. Memory Issues:**
```bash
# Reduce batch size
curl -X POST http://localhost:8000/train \
  -d '{"pair": "BTCUSDT", "batch_size": 32}'

# Monitor memory usage
docker stats trend-prediction-service
```

### Performance Optimization

**1. Faster Training:**
- Reduce epochs for initial testing
- Use smaller batch sizes on limited memory
- Reduce optimization trials for faster iteration

**2. Better Accuracy:**
- Increase optimization trials (16-20)
- Use larger datasets (>50,000 rows)
- Experiment with different time windows

**3. Resource Management:**
- Monitor system resources during training
- Use cleanup endpoints to manage disk space
- Restart service periodically for memory cleanup

## 📚 Technical Documentation

### Code Structure
```
app/
├── api/                    # FastAPI endpoints and routing
│   ├── routes/            # API route definitions
│   └── main.py           # API application setup
├── models/                # ML models and training logic
│   ├── trainer.py        # Training orchestration
│   ├── predictor.py      # Model inference
│   └── backtester.py     # Backtesting engine
├── utils/                 # Data processing and optimization
│   ├── data_processor.py # Data preprocessing
│   ├── technical_indicators.py # Technical analysis
│   ├── optuna_optimizer.py # Hyperparameter optimization
│   └── model_builder.py  # CNN architecture
├── core/                  # Configuration and settings
└── main.py               # Application entry point
```

### Key Components
- **CNN Model**: `app/models/cnn_model.py` - Core neural network architecture
- **Optuna Optimizer**: `app/utils/optuna_optimizer.py` - Bayesian optimization
- **Backtester**: `app/models/backtester.py` - Performance validation
- **Technical Indicators**: `app/utils/technical_indicators.py` - Financial analysis
- **API Routes**: `app/api/routes/` - REST API endpoints

### Testing
```bash
# Run unit tests
docker exec trend-prediction-service python -m pytest

# Run integration tests
docker exec trend-prediction-service python -m pytest tests/integration/

# Performance testing
docker exec trend-prediction-service python -m pytest tests/performance/
```

---

## 🎯 Summary

This Cryptocurrency Trend Prediction Service represents a production-ready implementation of advanced machine learning techniques for financial forecasting. The system combines:

- **Scientific Rigor**: Proper validation, realistic backtesting, and statistical significance
- **Technical Excellence**: Modern architecture, optimization, and monitoring
- **Production Readiness**: Containerization, scalability, and enterprise features
- **Extensibility**: Modular design for easy customization and enhancement
- **Complete Documentation**: Comprehensive deployment and usage guide

The service is designed for deployment in professional trading environments where accuracy, reliability, and performance are critical requirements.

**Note**: This system is for educational and research purposes. Cryptocurrency trading involves significant risk, and past performance does not guarantee future results. Always conduct thorough testing and risk assessment before deploying in production environments.

**License**: This software is proprietary and exclusively licensed. Unauthorized distribution, modification, or use is strictly prohibited.
