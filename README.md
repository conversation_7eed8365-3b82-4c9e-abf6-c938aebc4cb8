# Cryptocurrency Trend Prediction Service

A production-ready containerized machine learning service for cryptocurrency trend prediction using advanced Convolutional Neural Networks with technical indicator image transformation and Bayesian hyperparameter optimization.

## 🎯 Technical Overview

This service implements a sophisticated machine learning pipeline that transforms financial time series data into image representations for CNN-based trend prediction. The system provides enterprise-grade cryptocurrency trend forecasting through a comprehensive RESTful API with automated model optimization and deployment capabilities.

### Core Technical Features

- **Advanced CNN Architecture**: Multi-layer convolutional neural networks optimized for financial pattern recognition
- **Technical Indicator Transformation**: 30+ technical indicators converted to 6x6x3 image tensors for CNN processing
- **Bayesian Hyperparameter Optimization**: Automated model tuning using Optuna with Tree-structured Parzen Estimator
- **Production-Ready Deployment**: Containerized microservice architecture with Docker and FastAPI
- **Real-time Inference Pipeline**: Sub-second prediction latency with optimized model serving
- **Comprehensive Backtesting Framework**: Statistical validation with realistic trading simulation and risk metrics
- **Automated Model Management**: Version control, performance tracking, and automatic cleanup
- **Enterprise Monitoring**: System resource monitoring, training progress tracking, and error handling

## 🏗️ System Architecture

### Machine Learning Pipeline

```
Raw Market Data → Technical Indicators → Image Transformation → CNN Model → Trend Prediction
     ↓                    ↓                      ↓               ↓              ↓
  OHLCV Data         RSI, MACD, BB, etc.    6x6x3 Tensors    Pattern Recognition  Binary Classification
```

### Technical Stack

- **Backend Framework**: FastAPI with async/await for high-performance API serving
- **Machine Learning**: TensorFlow/Keras with custom CNN architectures
- **Optimization**: Optuna for Bayesian hyperparameter optimization with pruning
- **Data Processing**: Pandas, NumPy, TA-Lib for technical analysis
- **Containerization**: Docker with multi-stage builds and ARM64/x86_64 support
- **API Documentation**: Automatic OpenAPI/Swagger documentation generation
- **Monitoring**: Built-in system resource monitoring and performance tracking

### CNN Model Architecture

**Input Processing:**
- **Data Format**: OHLCV (Open, High, Low, Close, Volume) hourly data
- **Technical Indicators**: 30+ indicators across momentum, trend, volatility, volume categories
- **Image Transformation**: Reshape indicators into 6x6x3 matrices for spatial pattern recognition
- **Temporal Labeling**: 24-hour trend prediction using moving average methodology

**Network Design:**
- **Layer 1**: Conv2D with configurable filters (12-40), kernel size (2-3), dropout (0.05-0.35)
- **Layer 2**: Conv2D with configurable filters (24-80), kernel size (2-3), dropout (0.02-0.25)
- **Layer 3**: Dense layer with configurable nodes (16-80), dropout (0.1-0.5)
- **Output**: Binary classification (trend up/down) with softmax activation
- **Optimization**: Adam optimizer with configurable learning rate (0.0003-0.01)

### Hyperparameter Optimization

**Optuna Integration:**
- **Algorithm**: Tree-structured Parzen Estimator (TPE) for intelligent parameter search
- **Pruning**: MedianPruner for early stopping of unpromising trials
- **Search Space**: Expanded parameter ranges with categorical and continuous variables
- **Efficiency**: Reduced epochs per trial (15) for faster optimization cycles
- **Memory Management**: Automatic cleanup and garbage collection between trials

## 🔬 Data Processing Methodology

### Technical Indicator Calculation

The service implements a comprehensive technical analysis framework:

**Momentum Indicators:**
- RSI (Relative Strength Index) with multiple periods
- Stochastic Oscillator (%K, %D)
- Williams %R
- Rate of Change (ROC)

**Trend Indicators:**
- MACD (Moving Average Convergence Divergence)
- ADX (Average Directional Index)
- CCI (Commodity Channel Index)

**Volatility Indicators:**
- ATR (Average True Range)
- Bollinger Bands (Upper, Middle, Lower)

**Volume Indicators:**
- MFI (Money Flow Index)
- OBV (On-Balance Volume)

**Moving Averages:**
- Simple Moving Averages (SMA) - multiple periods
- Exponential Moving Averages (EMA) - multiple periods

### Image Transformation Process

1. **Normalization**: Scale all indicators to [0, 1] range
2. **Reshaping**: Transform 30+ indicators into 6x6x3 tensor format
3. **Spatial Arrangement**: Organize indicators by category for pattern recognition
4. **Temporal Consistency**: Maintain chronological order for time series integrity

### Data Validation and Quality Control

- **Missing Data Handling**: Forward fill and interpolation strategies
- **Outlier Detection**: Statistical methods for anomaly identification
- **Data Integrity**: Validation of OHLCV consistency and volume correlation
- **Temporal Validation**: Strict chronological ordering and gap detection

## 🚀 Quick Start & Deployment

### Prerequisites

- Docker and Docker Compose
- 4GB+ RAM recommended
- ARM64 or x86_64 architecture

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd predictmarket-TrendPredictionService
```

2. **Start the service**
```bash
# For ARM64 (Apple Silicon)
docker-compose -f docker-compose.arm64.yml up --build -d

# For x86_64
docker-compose up --build -d
```

3. **Verify installation**
```bash
curl http://localhost:8000/health
# Expected: {"status":"ok","timestamp":"..."}
```

4. **Access API documentation**
```
http://localhost:8000/docs
```

## 🔧 API Reference

### Core Endpoints

#### Health Check
```http
GET /health
```
Returns service health status and timestamp.

#### Dataset Management
```http
GET /datasets
```
Lists all available datasets with metadata (size, last modified, etc.).

#### Model Training with Automatic Optimization
```http
POST /train
Content-Type: application/json

{
  "epochs": 64,
  "batch_size": 80,
  "learning_rate": 0.001,
  "pair": "BTCUSDT",
  "auto_optimize": true,
  "optimization_trials": 16,
  "optimization_timeout": 800
}
```

**Key Features:**
- `auto_optimize`: Enable automatic hyperparameter optimization (default: true)
- `optimization_trials`: Number of Optuna trials (default: 16)
- `optimization_timeout`: Timeout in seconds (default: 800)

#### Training Progress Monitoring
```http
GET /training/status?pair=BTCUSDT
```
Returns real-time training progress, current epoch, metrics, elapsed time, system monitoring, and optimization results.

#### Model Inference
```http
POST /predict
Content-Type: application/json

{
  "market_data": [
    {
      "timestamp": 1625097600000,
      "open": 35000.0,
      "high": 36000.0,
      "low": 34500.0,
      "close": 35500.0,
      "volume": 1000.0,
      "symbol": "BTCUSDT"
    }
  ]
}
```

#### Professional Backtesting
```http
POST /backtest
Content-Type: application/json

{
  "model_path": "models/BTCUSDT/best_model.h5",
  "data_path": "data/BTCUSDT_1h.csv",
  "window": 24,
  "generate_plots": false
}
```

#### Model Management
```http
GET /models
```
Lists all trained models with performance metrics and metadata.

#### Model Cleanup
```http
POST /models/cleanup
GET /models/cleanup/status
```
Automatically clean up old and underperforming models to free disk space.

## 📊 Backtesting Framework

### Methodology

The backtesting engine implements professional trading simulation with:

**Risk Management:**
- Position sizing: 2% of capital per trade
- Transaction costs: 0.1% per trade
- Stop-loss and take-profit mechanisms
- Maximum drawdown tracking

**Statistical Validation:**
- Temporal data separation (no look-ahead bias)
- Out-of-sample testing
- Walk-forward analysis
- Monte Carlo simulation support

**Performance Metrics:**
- **Accuracy**: Prediction correctness percentage
- **F1 Score**: Harmonic mean of precision and recall
- **Sharpe Ratio**: Risk-adjusted returns (capped at 5.0 for realism)
- **Profit Factor**: Gross profit / gross loss ratio
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Win Rate**: Percentage of profitable trades
- **Total Return**: Cumulative portfolio performance

### Validation Standards

- **No Overfitting**: Strict temporal validation with separated test sets
- **Realistic Returns**: Conservative position sizing and transaction costs
- **Statistical Significance**: Minimum trade count and confidence intervals
- **Market Regime Testing**: Performance across different market conditions

## 🏭 Production Deployment

### System Requirements

**Minimum:**
- 2 CPU cores
- 4GB RAM
- 5GB disk space
- Docker support

**Recommended:**
- 4+ CPU cores
- 8GB+ RAM
- 20GB+ disk space
- SSD storage

### Monitoring & Observability

**Built-in Monitoring:**
- Real-time system resource tracking (CPU, memory, disk)
- Training progress monitoring with live metrics
- Model performance tracking and comparison
- Automatic error detection and logging

**Health Checks:**
- Service availability monitoring
- Model loading verification
- Data pipeline validation
- API endpoint testing

### Scalability Considerations

**Horizontal Scaling:**
- Stateless API design for load balancing
- Containerized deployment for orchestration
- Separate training and inference services
- Database-backed model storage

**Performance Optimization:**
- Model caching for faster inference
- Batch prediction support
- Asynchronous training operations
- Resource-aware scheduling

## 🔒 Security & Compliance

### Data Security
- No sensitive data persistence
- Secure API endpoints
- Input validation and sanitization
- Error handling without data leakage

### Model Security
- Model versioning and integrity checks
- Secure model storage
- Access control for training operations
- Audit logging for compliance

## 📈 Performance Characteristics

### Training Performance
- Optimization time: 2-5 minutes for 16 trials
- Training time: 3-8 minutes for 64-96 epochs
- Memory usage: 1-2GB during training
- CPU utilization: 15-25% during optimization

### Inference Performance
- Prediction latency: <100ms
- Throughput: 100+ predictions/second
- Memory footprint: <500MB at rest
- Model loading time: <5 seconds

### Backtesting Performance
- Historical analysis: 2,000+ trades in <30 seconds
- Metrics calculation: Real-time
- Memory efficiency: Streaming data processing
- Scalable to multi-year datasets

## 🛠️ Development & Customization

### Adding New Cryptocurrency Pairs

1. Add CSV data file to `data/` directory with format: `{PAIR}_1h.csv`
2. Ensure OHLCV format with proper timestamps
3. Service automatically detects new datasets
4. Train models using the `/train` endpoint

### Extending Technical Indicators

1. Implement new indicators in `app/utils/technical_indicators.py`
2. Update image transformation in `app/utils/data_processing.py`
3. Maintain 6x6x3 tensor format for CNN compatibility
4. Test with existing model architecture

### Custom Model Architectures

1. Modify CNN architecture in `app/models/cnn_model.py`
2. Update hyperparameter search space in `app/utils/optuna_optimizer.py`
3. Ensure compatibility with image input format
4. Validate with backtesting framework

## 📚 Technical Documentation

### Code Structure
```
app/
├── api/              # FastAPI endpoints and routing
├── models/           # ML models and training logic
├── utils/            # Data processing and optimization
├── core/             # Configuration and settings
└── main.py           # Application entry point
```

### Key Components
- **CNN Model**: `app/models/cnn_model.py`
- **Optuna Optimizer**: `app/utils/optuna_optimizer.py`
- **Backtester**: `app/models/backtester.py`
- **Technical Indicators**: `app/utils/technical_indicators.py`
- **API Routes**: `app/api/routes/`

### Testing
```bash
# Run unit tests
docker exec trend-prediction-service python -m pytest

# Run integration tests
docker exec trend-prediction-service python -m pytest tests/integration/

# Performance testing
docker exec trend-prediction-service python -m pytest tests/performance/
```

---

## 🎯 Summary

This Cryptocurrency Trend Prediction Service represents a production-ready implementation of advanced machine learning techniques for financial forecasting. The system combines:

- **Scientific Rigor**: Proper validation, realistic backtesting, and statistical significance
- **Technical Excellence**: Modern architecture, optimization, and monitoring
- **Production Readiness**: Containerization, scalability, and enterprise features
- **Extensibility**: Modular design for easy customization and enhancement

The service is designed for deployment in professional trading environments where accuracy, reliability, and performance are critical requirements.

**Note**: This system is for educational and research purposes. Cryptocurrency trading involves significant risk, and past performance does not guarantee future results. Always conduct thorough testing and risk assessment before deploying in production environments.
