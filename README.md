# Cryptocurrency Trend Prediction Service

A production-ready, containerized machine learning service designed for cryptocurrency trend prediction using Convolutional Neural Networks (CNN). Implements the validated CNN v2.0 methodology to transform technical indicators into image-like matrices for deep learning-based financial forecasting.

## Overview

This service provides comprehensive capabilities:

* **Technical Indicator Computation**: Over 30 technical indicators computed from OHLCV data.
* **Image Transformation**: Converts indicators into 6x6x3 image matrices for CNN input.
* **CNN Model Training**: Robust CNN architecture for 24-hour trend forecasting.
* **Real-time API**: RESTful endpoints for training, predictions, and management.
* **Hyperparameter Optimization**: Automated optimization for peak performance.
* **Professional Backtesting**: Temporal data splitting with detailed metrics.
* **Multi-pair Support**: Scalable architecture supporting various cryptocurrency pairs.

## Technical Architecture

### Core Components

* **API Framework**: FastAPI with auto-generated OpenAPI documentation.
* **ML Engine**: TensorFlow/Keras for training and inference.
* **Data Processing**: Pandas/NumPy for efficient data handling.
* **Technical Analysis**: Custom calculations for 30+ indicators.
* **Containerization**: Docker with ARM64 and x86\_64 compatibility.
* **Storage**: File-based persistence with JSON metadata.

### CNN Model Structure

```plaintext
Input: 6x6x3 Image Matrix
   ↓
Conv2D (20 filters, 2x2 kernel, dropout 0.22)
   ↓
Conv2D (40 filters, 2x2 kernel, dropout 0.05)
   ↓
Flatten + Dense (32 nodes, dropout 0.22)
   ↓
Output: Binary Classification (Trend Up/Down)
```

## Performance Metrics

| Metric              | Typical Range | Notes                        |
| ------------------- | ------------- | ---------------------------- |
| Training Accuracy   | 55-70%        | Market-dependent             |
| Validation F1 Score | 50-75%        | Classification effectiveness |
| Training Time       | 1-5 mins      | 64 epochs, \~30K samples     |
| Model Size          | \~94 KB       | Efficient deployment         |
| Inference Latency   | <100 ms       | Single prediction            |

*Actual results vary by data quality and optimization.*

## Quick Start

### Requirements

* Docker & Docker Compose
* Recommended: 4GB+ RAM
* ARM64 or x86\_64 architecture

### Installation

```bash
git clone <repository-url>
cd predictmarket-TrendPredictionService
```

Start the service:

```bash
# ARM64 (Apple Silicon)
docker-compose -f docker-compose.arm64.yml up --build -d

# x86_64
docker-compose up --build -d
```

Verify installation:

```bash
curl http://localhost:8000/health
# Response: {"status":"ok","timestamp":"..."}
```

API docs at: [http://localhost:8000/docs](http://localhost:8000/docs)

## API Reference

### Health Check

`GET /health` – Service health status.

### Dataset Management

`GET /datasets` – Lists datasets and metadata.

### Model Training

`POST /train` – Starts training process.

```json
{
  "epochs": 64,
  "batch_size": 80,
  "learning_rate": 0.001,
  "pair": "BTCUSDT",
  "data_path": "data/BTCUSDT_1h.csv"
}
```

### Hyperparameter Optimization

`POST /optimize` – Runs automated optimization.

### Training Status

`GET /training/status?pair=BTCUSDT` – Training progress.

### Prediction

`POST /predict` – Makes a prediction.

### Backtesting

`POST /backtest` – Evaluates model performance.

### Model Management

`GET /models` – Lists trained models.

## Project Structure

```plaintext
predictmarket-TrendPredictionService/
├── app/
│   ├── api/
│   │   └── main.py
│   ├── models/
│   │   ├── trainer.py
│   │   ├── predictor.py
│   │   └── backtester.py
│   └── utils/
│       ├── data_processor.py
│       ├── model_builder.py
│       ├── indicators.py
│       ├── hyperparameter_optimizer.py
│       └── config.py
├── data/
│   └── {PAIR}_1h.csv
├── models/
├── docker-compose.yml
├── docker-compose.arm64.yml
├── Dockerfile
├── requirements.txt
└── README.md
```

## Deployment

### Development

```bash
docker-compose -f docker-compose.arm64.yml up --build
```

### Production

```bash
docker-compose -f docker-compose.arm64.yml up --build -d --scale trend-prediction-api=3
```

## Monitoring and Logging

* **Health Checks**: `/health`, `/status`
* **Logging**: Structured, configurable logging levels

## Testing and Validation

End-to-end and individual component testing via provided scripts and curl commands.

## Troubleshooting

Refer to logs and provided diagnostic commands in documentation.

## Contributing

* Fork, clone, install dependencies
* Follow PEP 8 and full type annotations
* Include unit tests and comprehensive documentation

## License

Not Specified.

## Acknowledgments

* CNN v2.0 Research Methodology
* TensorFlow/Keras
* FastAPI framework
* Technical analysis contributors