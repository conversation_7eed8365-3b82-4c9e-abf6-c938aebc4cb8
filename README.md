# Cryptocurrency Trend Prediction Service

A production-ready containerized machine learning service for cryptocurrency trend prediction using Convolutional Neural Networks (CNN). This service implements the CNN v2.0 methodology for transforming technical indicators into image-like matrices for deep learning-based financial forecasting.

## Overview

This service provides a comprehensive solution for cryptocurrency trend prediction with the following capabilities:

- **Technical Indicator Computation**: Calculates 30+ technical indicators from OHLCV market data
- **Image Transformation**: Converts technical indicators into 6x6x3 image matrices for CNN processing
- **CNN Model Training**: Implements research-validated CNN architecture for 24-hour trend prediction
- **Intelligent Optimization**: Automated hyperparameter optimization using Optuna with Bayesian search
- **Real-time API**: RESTful API endpoints for training, prediction, and model management
- **Professional Backtesting**: Temporal data splitting with comprehensive performance metrics
- **Multi-pair Support**: Extensible architecture supporting multiple cryptocurrency pairs

## Technical Architecture

### Core Components

- **API Layer**: FastAPI framework with automatic OpenAPI documentation
- **ML Engine**: TensorFlow/Keras for CNN model training and inference
- **Optimization Engine**: Optuna for intelligent hyperparameter optimization with pruning
- **Data Processing**: Pandas/NumPy for efficient data manipulation and feature engineering
- **Technical Analysis**: Custom implementation of 13 indicator types generating 30+ features
- **Containerization**: Docker with ARM64 and x86_64 support
- **Storage**: File-based model persistence with comprehensive JSON metadata

### Model Architecture

\`\`\`
Input: 6x6x3 Image Matrix (Technical Indicators)
    ↓
Conv2D Layer 1: 20 filters, 2x2 kernel, dropout=0.22
    ↓
Conv2D Layer 2: 40 filters, 2x2 kernel, dropout=0.05
    ↓
Flatten + Dense Layer: 32 nodes, dropout=0.22
    ↓
Output Layer: 2 nodes (Binary Classification)
\`\`\`

**Model Specifications:**
- Parameters: ~24K (efficient for deployment)
- Input Shape: (batch_size, 6, 6, 3)
- Output: Binary classification (trend up/down)
- Prediction Window: 24 hours ahead
- Training Method: Temporal cross-validation with early stopping

## Performance Characteristics

Performance metrics vary based on market conditions, data quality, and hyperparameter configuration. Typical ranges observed during development:

| Metric | Range | Notes |
|--------|-------|-------|
| Training Accuracy | 55-70% | Depends on market volatility |
| Validation F1 Score | 50-75% | Binary classification performance |
| Training Time | 1-5 minutes | 64 epochs on 30K+ samples |
| Model Size | ~94KB | Efficient for production deployment |
| Inference Speed | <100ms | Single prediction latency |
| Optimization Time | 5-15 minutes | 12 trials with intelligent pruning |

*Note: Actual performance depends on data quality, market conditions, and hyperparameter optimization.*

## Quick Start

### Prerequisites

- Docker and Docker Compose
- 4GB+ RAM recommended
- ARM64 or x86_64 architecture

### Installation

1. **Clone the repository**
\`\`\`bash
git clone <repository-url>
cd predictmarket-TrendPredictionService
\`\`\`

2. **Start the service**
\`\`\`bash
# For ARM64 (Apple Silicon)
docker-compose -f docker-compose.arm64.yml up --build -d

# For x86_64
docker-compose up --build -d
\`\`\`

3. **Verify installation**
\`\`\`bash
curl http://localhost:8000/health
# Expected: {"status":"ok","timestamp":"..."}
\`\`\`

4. **Access API documentation**
\`\`\`
http://localhost:8000/docs
\`\`\`

## API Reference

### Core Endpoints

#### Health Check
\`\`\`http
GET /health
\`\`\`
Returns service health status and timestamp.

#### Dataset Management
\`\`\`http
GET /datasets
\`\`\`
Lists all available datasets with metadata (size, last modified, etc.).

#### Model Training with Automatic Optimization
\`\`\`http
POST /train
Content-Type: application/json

{
  "epochs": 64,
  "batch_size": 80,
  "learning_rate": 0.001,
  "pair": "BTCUSDT",
  "auto_optimize": true,
  "optimization_trials": 12,
  "optimization_timeout": 600
}
\`\`\`

**New Features:**
- \`auto_optimize\`: Enable automatic hyperparameter optimization (default: true)
- \`optimization_trials\`: Number of Optuna trials (default: 12)
- \`optimization_timeout\`: Timeout in seconds (default: 600)

#### Legacy Hyperparameter Optimization
\`\`\`http
POST /optimize
Content-Type: application/json

{
  "pair": "BTCUSDT",
  "optimization_type": "random_search",
  "max_trials": 20,
  "metric": "f1_score"
}
\`\`\`

#### Training Progress Monitoring
\`\`\`http
GET /training/status?pair=BTCUSDT
\`\`\`
Returns real-time training progress, current epoch, metrics, elapsed time, and optimization results.

#### Model Inference
\`\`\`http
POST /predict
Content-Type: application/json

{
  "market_data": [
    {
      "timestamp": 1625097600000,
      "open": 35000.0,
      "high": 36000.0,
      "low": 34500.0,
      "close": 35500.0,
      "volume": 1000.0,
      "symbol": "BTCUSDT"
    }
  ]
}
\`\`\`

#### Model Backtesting
\`\`\`http
POST /backtest
Content-Type: application/json

{
  "model_path": "models/BTCUSDT/BTCUSDT_best_model.h5",
  "data_path": "data/BTCUSDT_1h.csv",
  "window": 24,
  "generate_plots": false
}
\`\`\`

#### Model Management
\`\`\`http
GET /models
\`\`\`
Lists all trained models with performance metrics and metadata.

## Hyperparameter Optimization

### Optuna Integration

The service now uses **Optuna** for intelligent hyperparameter optimization with the following features:

#### Bayesian Optimization
- **Algorithm**: Tree-structured Parzen Estimator (TPE)
- **Learning**: Learns from previous trials to suggest better parameters
- **Efficiency**: Significantly faster than grid/random search

#### Automatic Pruning
- **Early Stopping**: Automatically stops unpromising trials
- **Resource Efficiency**: Saves computational time and memory
- **Intelligent Decisions**: Based on intermediate validation loss

#### Optimized Parameters
\`\`\`python
{
    "epochs": [32, 48, 64, 80],                    # Training epochs
    "batch_size": [40, 60, 80, 100],              # Batch size
    "learning_rate": [0.0005, 0.005],             # Learning rate (log scale)
    "conv_filters_1": [16, 32],                   # First conv layer filters
    "conv_filters_2": [32, 64],                   # Second conv layer filters
    "dense_nodes": [24, 64],                      # Dense layer nodes
    "dropout_conv_1": [0.1, 0.3],                # First conv dropout
    "dropout_conv_2": [0.05, 0.2],               # Second conv dropout
    "dropout_dense": [0.15, 0.4]                 # Dense layer dropout
}
\`\`\`

### Optimization Workflow

1. **Automatic Integration**: Optimization runs automatically during training (unless disabled)
2. **Quick Trials**: Each trial limited to 15 epochs for speed
3. **Intelligent Pruning**: Poor trials stopped early
4. **Full Training**: Best parameters used for complete 64-epoch training
5. **Fallback**: Research-validated defaults if optimization fails

## Technical Implementation

### Data Processing Pipeline

1. **Data Ingestion**: CSV files with OHLCV + volume data
2. **Technical Indicator Calculation**: 13 indicator types producing 30+ features
3. **Feature Engineering**: Moving average labels for supervised learning
4. **Image Transformation**: Reshape indicators into 6x6x3 matrices
5. **Temporal Splitting**: Chronological train/validation/test splits
6. **Model Training**: CNN with early stopping and learning rate scheduling

### Technical Indicators

The service calculates the following technical indicators:

| Category | Indicators | Features Generated |
|----------|------------|-------------------|
| **Momentum** | RSI, Stochastic, Williams %R, ROC | 6 features |
| **Trend** | MACD, ADX, CCI | 6 features |
| **Volatility** | ATR, Bollinger Bands | 5 features |
| **Volume** | MFI, OBV | 2 features |
| **Moving Averages** | SMA, EMA (multiple periods) | 10+ features |

### CNN Methodology

The service implements the original CNN v2.0 research methodology:

1. **Technical Indicator Calculation**
\`\`\`python
# Calculate 30+ technical indicators
indicators = ['RSI', 'MACD', 'ATR', 'BB', 'STOCH', 'ADX', 'WillR', 'CCI', 'ROC', 'SMA', 'EMA', 'MFI', 'OBV']
df_with_indicators = calculate_technical_indicators(df, 'close', indicators)
\`\`\`

2. **Moving Average Label Creation**
\`\`\`python
# Create binary labels based on 24-period Simple Moving Average
future_sma = df['SMA'].shift(-24)
current_sma = df['SMA']
labels = (future_sma > current_sma).astype(int)  # 1=upward, 0=downward
\`\`\`

3. **Image Transformation**
\`\`\`python
# Transform 30 indicators into 6x6x3 image matrices
image_data = reshape_as_image(indicators, width=6, height=6, channels=3)
\`\`\`

4. **CNN Architecture**
\`\`\`python
# Research-validated CNN parameters (optimized by Optuna)
model_params = {
    'conv2d_layers': {
        'filters_1': 20, 'kernel_size_1': 2, 'dropout_1': 0.22,
        'filters_2': 40, 'kernel_size_2': 2, 'dropout_2': 0.05
    },
    'dense_layers': {
        'nodes_1': 32, 'dropout_1': 0.22
    },
    'epochs': 64,
    'batch_size': 80,
    'learning_rate': 0.001
}
\`\`\`

### Data Storage and Persistence

#### Model Storage Structure
\`\`\`
models/
├── {PAIR}/                           # Per-cryptocurrency pair
│   ├── {PAIR}_model_{timestamp}.h5   # Trained model weights
│   ├── {PAIR}_best_model.h5          # Symlink to best performing model
│   ├── {PAIR}_history_{timestamp}.json    # Training history
│   └── {PAIR}_metadata_{timestamp}.json  # Comprehensive model metadata
├── optimization_results/             # Optuna optimization results
│   ├── optuna_optimization_{timestamp}.json
│   └── optuna_summary_{timestamp}.json
└── backtest_results/                 # Backtesting results
    └── backtest_{timestamp}.json
\`\`\`

#### Enhanced Model Metadata
\`\`\`json
{
  "timestamp": "20250529_184127",
  "pair": "BTCUSDT",
  "model_info": {
    "architecture": "CNN_v2.0",
    "input_shape": [6, 6, 3],
    "parameters": 24078,
    "model_size_mb": 0.09
  },
  "training_params": {
    "epochs_requested": 64,
    "epochs_trained": 64,
    "batch_size": 80,
    "learning_rate": 0.001,
    "auto_optimize": true
  },
  "optimization_info": {
    "enabled": true,
    "best_score": 0.6615,
    "n_trials": 12,
    "study_name": "cnn_optimization_BTCUSDT_20250529"
  },
  "performance_metrics": {
    "training_accuracy": 0.6348,
    "training_f1_score": 0.6615,
    "validation_loss": 0.5382,
    "training_samples": 31840,
    "validation_samples": 5970
  }
}
\`\`\`

## Project Structure

\`\`\`
predictmarket-TrendPredictionService/
├── app/
│   ├── api/
│   │   └── main.py                    # FastAPI application and routes
│   ├── models/
│   │   ├── trainer.py                 # Enhanced model training with Optuna
│   │   ├── predictor.py               # Inference service
│   │   └── backtester.py              # Model validation and backtesting
│   └── utils/
│       ├── data_processor.py          # Data preprocessing pipeline
│       ├── model_builder.py           # CNN architecture definition
│       ├── indicators.py              # Technical analysis calculations
│       ├── optuna_optimizer.py        # Intelligent hyperparameter optimization
│       ├── model_cleanup.py           # Automatic model cleanup utility
│       └── config.py                  # Configuration management
├── data/
│   └── {PAIR}_1h.csv                 # Market data (OHLCV format)
├── models/                           # Model storage (created at runtime)
├── docker-compose.yml               # x86_64 deployment configuration
├── docker-compose.arm64.yml         # ARM64 deployment configuration
├── Dockerfile                       # Container build instructions
├── requirements.txt                 # Python dependencies (includes Optuna)
└── README.md                        # This documentation
\`\`\`

## Configuration

### Environment Variables

\`\`\`bash
# Optional configuration
PYTHONPATH=/app                      # Python module path
TF_CPP_MIN_LOG_LEVEL=2              # TensorFlow logging level
MODEL_STORAGE_PATH=/app/models       # Model persistence directory
DATA_PATH=/app/data                  # Data directory
\`\`\`

### Model Parameters

Default parameters based on research validation and Optuna optimization:

\`\`\`python
DEFAULT_PARAMS = {
    'epochs': 64,                    # Training epochs
    'batch_size': 80,               # Batch size for training
    'learning_rate': 0.001,         # Adam optimizer learning rate
    'auto_optimize': True,          # Enable automatic optimization
    'optimization_trials': 12,      # Number of Optuna trials
    'optimization_timeout': 600,    # Optimization timeout (seconds)
    'early_stopping_patience': 100, # Early stopping patience
    'validation_split': 0.2,        # Temporal validation split
}
\`\`\`

## Deployment

### Development Environment

\`\`\`bash
# Start development server with hot reload
docker-compose -f docker-compose.arm64.yml up --build

# View logs
docker logs trend-prediction-service --follow

# Access interactive API documentation
open http://localhost:8000/docs
\`\`\`

### Production Deployment

\`\`\`bash
# Production deployment with scaling
docker-compose -f docker-compose.arm64.yml up --build -d --scale trend-prediction-api=3

# Health monitoring
curl http://localhost:8000/health

# Training with optimization
curl -X POST http://localhost:8000/train \\
  -H "Content-Type: application/json" \\
  -d '{"pair": "BTCUSDT", "auto_optimize": true, "optimization_trials": 12}'
\`\`\`

## Code Quality and Robustness

### Optimizer Improvements

- **Memory Management**: Automatic cleanup with TensorFlow session clearing
- **Error Handling**: Comprehensive exception handling with fallback mechanisms
- **Pruning Integration**: Native Optuna pruning callbacks for efficiency
- **Result Persistence**: Detailed optimization history and summary files
- **Public API**: Clean interface for parameter conversion and access

### Trainer Enhancements

- **Comprehensive Metadata**: Detailed model information including optimization results
- **Automatic Integration**: Seamless Optuna integration with fallback to defaults
- **Progress Tracking**: Enhanced training status with optimization information
- **Resource Efficiency**: Proper memory management and cleanup

### Production Features

- **Robust Error Handling**: Graceful degradation when optimization fails
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Performance Monitoring**: Built-in metrics tracking and reporting
- **Scalable Architecture**: Container-ready with health checks

## Testing and Validation

### End-to-End Testing

\`\`\`bash
# Complete workflow test with optimization
curl -X POST http://localhost:8000/train \\
  -H "Content-Type: application/json" \\
  -d '{"pair": "BTCUSDT", "auto_optimize": true, "optimization_trials": 5}'

# Monitor training progress
curl http://localhost:8000/training/status?pair=BTCUSDT

# Backtest optimized model
curl -X POST http://localhost:8000/backtest \\
  -H "Content-Type: application/json" \\
  -d '{"model_path": "models/BTCUSDT/BTCUSDT_best_model.h5"}'
\`\`\`

### Data Validation

- **Temporal Integrity**: Strict chronological data splitting
- **No Data Leakage**: Separate train/validation/test sets
- **Feature Validation**: Technical indicator range checking
- **Model Validation**: Cross-validation and backtesting

## Troubleshooting

### Common Issues

1. **Optimization Timeout**
   - Increase \`optimization_timeout\` parameter
   - Reduce \`optimization_trials\` for faster completion
   - Check system resources and memory availability

2. **Memory Issues During Optimization**
   - Reduce batch size in optimization space
   - Enable automatic pruning (default: enabled)
   - Monitor container memory limits

3. **Performance Issues**
   - Use GPU acceleration if available
   - Optimize data loading pipeline
   - Consider model quantization for inference

### Debug Commands

\`\`\`bash
# Container diagnostics
docker exec -it trend-prediction-service bash
docker stats trend-prediction-service

# Application logs
docker logs trend-prediction-service --since 1h

# Optimization results
curl http://localhost:8000/training/status?pair=BTCUSDT | jq '.optimization'

# Model inspection
curl http://localhost:8000/models | jq '.models[0]'
\`\`\`

## Contributing

### Development Setup

1. **Fork and clone the repository**
2. **Set up development environment**
3. **Install pre-commit hooks**
4. **Run tests before submitting PRs**

### Code Standards

- **Python**: PEP 8 compliance with type hints
- **Documentation**: Comprehensive docstrings and comments
- **Testing**: Unit tests for all components
- **Optimization**: Performance-conscious code with memory management

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Original CNN v2.0 research methodology
- Optuna hyperparameter optimization framework
- TensorFlow/Keras deep learning framework
- FastAPI web framework
- Technical Analysis Library contributors

---

**Production-ready cryptocurrency trend prediction service with intelligent Optuna optimization, comprehensive API, and professional backtesting capabilities.**
