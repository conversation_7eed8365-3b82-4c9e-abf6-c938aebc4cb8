{"timestamp": "20250530_100944", "pair": "BTCUSDT", "model_info": {"architecture": "CNN_v2.0", "input_shape": [6, 6, 3], "parameters": 24078, "model_size_mb": 0.32}, "training_params": {"epochs_requested": 32, "epochs_trained": 32, "batch_size": 80, "learning_rate": 0.001, "optimizer": "adam", "early_stopping": true, "auto_optimize": false}, "optimization_info": {"enabled": false}, "performance_metrics": {"training_accuracy": 0.6612234643888959, "training_f1_score": 0.7035938015166502, "validation_loss": 0.5432448387145996, "final_training_loss": 0.48031002283096313, "best_val_loss": 0.5432448387145996, "training_samples": 31840, "validation_samples": 7961}, "data_info": {"total_samples": 40000, "features_count": 30, "data_source": "data/BTCUSDT_1h.csv"}, "file_paths": {"model_path": "/app/models/BTCUSDT/BTCUSDT_model_20250530_100944.h5", "history_path": "/app/models/BTCUSDT/BTCUSDT_history_20250530_100944.json", "metadata_path": "/app/models/BTCUSDT/BTCUSDT_model_20250530_100944_metadata.json"}, "usage_info": {"last_used": "20250530_100944", "usage_count": 0, "created_by": "Model<PERSON><PERSON>er", "version": "2.0"}}